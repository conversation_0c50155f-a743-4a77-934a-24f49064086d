<?php

/**
 * WooCommerce integration functionality
 *
 * Handles order creation and integration with WooCommerce
 */

class USF_WooCommerce {

    /**
     * Create WooCommerce order from accepted bid
     */
    public static function create_order_from_bid($bid) {
        if (!class_exists('WooCommerce')) {
            return array(
                'success' => false,
                'message' => 'WooCommerce is not active'
            );
        }

        try {
            // Get lot details
            $lot = USF_Database::get_lot($bid->lot_id);
            if (!$lot) {
                return array(
                    'success' => false,
                    'message' => 'Auction lot not found'
                );
            }

            // Create or get product for this lot
            $product_id = self::create_auction_product($lot, $bid);
            if (!$product_id) {
                return array(
                    'success' => false,
                    'message' => 'Failed to create product'
                );
            }

            // Create WooCommerce order using HPOS-compatible method
            $order = new WC_Order();
            
            // Set customer information
            $order->set_billing_first_name(self::get_first_name($bid->user_name));
            $order->set_billing_last_name(self::get_last_name($bid->user_name));
            $order->set_billing_email($bid->user_email);
            $order->set_billing_phone($bid->user_phone);
            
            // Set shipping same as billing for virtual products
            $order->set_shipping_first_name(self::get_first_name($bid->user_name));
            $order->set_shipping_last_name(self::get_last_name($bid->user_name));
            
            // Get product and add to order
            $product = wc_get_product($product_id);
            if (!$product) {
                return array(
                    'success' => false,
                    'message' => 'Product not found'
                );
            }
            
            // Create order item
            $item = new WC_Order_Item_Product();
            $item->set_product($product);
            $item->set_quantity(1);
            $item->set_subtotal($bid->bid_amount);
            $item->set_total($bid->bid_amount);
            $item->set_name($product->get_name());
            
            // Add item to order
            $order->add_item($item);

            // Set order totals
            $order->calculate_totals();

            // Add order notes
            $order_note = sprintf(
                'Order created from auction bid. Lot ID: %s, Bid Amount: $%s, Auction House: %s',
                $bid->lot_id,
                number_format($bid->bid_amount, 2),
                $lot->auction_house
            );
            $order->add_order_note($order_note);

            // Add custom meta data
            $order->update_meta_data('_usf_auction_lot_id', $bid->lot_id);
            $order->update_meta_data('_usf_auction_bid_id', $bid->id);
            $order->update_meta_data('_usf_auction_house', $lot->auction_house);
            $order->update_meta_data('_usf_bid_amount', $bid->bid_amount);

            // Set order status to pending payment
            $order->set_status('pending');
            
            // Save order
            $order->save();

            return array(
                'success' => true,
                'order_id' => $order->get_id(),
                'order' => $order
            );

        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Error creating order: ' . $e->getMessage()
            );
        }
    }

    /**
     * Create auction product for the lot
     */
    private static function create_auction_product($lot, $bid) {
        // Check if product already exists for this lot
        $existing_product = self::get_lot_product($lot->lot_id);
        if ($existing_product) {
            // Update price to bid amount
            $existing_product->set_regular_price($bid->bid_amount);
            $existing_product->set_price($bid->bid_amount);
            $existing_product->save();
            return $existing_product->get_id();
        }

        // Create new product
        $product = new WC_Product_Simple();
        
        // Set basic product information
        $product_name = sprintf('Auction Lot #%s - %s', $lot->lot_id, $lot->model);
        $product->set_name($product_name);
        
        // Create detailed description
        $description = self::create_product_description($lot);
        $product->set_description($description);
        $product->set_short_description(sprintf('%s %s - %s', $lot->model, $lot->memory, $lot->grade));
        
        // Set pricing
        $product->set_regular_price($bid->bid_amount);
        $product->set_price($bid->bid_amount);
        
        // Set as virtual product (no shipping)
        $product->set_virtual(true);
        $product->set_downloadable(false);
        
        // Set stock status
        $product->set_stock_status('instock');
        $product->set_manage_stock(false);
        
        // Set catalog visibility (hidden from catalog)
        $product->set_catalog_visibility('hidden');
        
        // Set product status
        $product->set_status('publish');
        
        // Add custom meta data
        $product->update_meta_data('_usf_auction_lot_id', $lot->lot_id);
        $product->update_meta_data('_usf_auction_house', $lot->auction_house);
        $product->update_meta_data('_usf_lot_grade', $lot->grade);
        $product->update_meta_data('_usf_lot_memory', $lot->memory);
        $product->update_meta_data('_usf_total_units', $lot->total_units);
        $product->update_meta_data('_usf_min_offer', $lot->min_offer);
        
        // Save product
        $product_id = $product->save();
        
        if ($product_id) {
            // Set product category
            wp_set_object_terms($product_id, array('auction-lots'), 'product_cat');
            
            return $product_id;
        }
        
        return false;
    }

    /**
     * Get existing product for lot
     */
    private static function get_lot_product($lot_id) {
        $products = wc_get_products(array(
            'meta_key' => '_usf_auction_lot_id',
            'meta_value' => $lot_id,
            'limit' => 1
        ));
        
        return !empty($products) ? $products[0] : null;
    }

    /**
     * Create detailed product description
     */
    private static function create_product_description($lot) {
        $description = sprintf('<h3>Auction Lot Details</h3>');
        $description .= sprintf('<p><strong>Lot ID:</strong> %s</p>', $lot->lot_id);
        $description .= sprintf('<p><strong>Auction House:</strong> %s</p>', $lot->auction_house);
        $description .= sprintf('<p><strong>Model:</strong> %s</p>', $lot->model);
        $description .= sprintf('<p><strong>Memory:</strong> %s</p>', $lot->memory);
        $description .= sprintf('<p><strong>Grade:</strong> %s</p>', $lot->grade);
        $description .= sprintf('<p><strong>Total Units:</strong> %d</p>', $lot->total_units);
        $description .= sprintf('<p><strong>Minimum Offer:</strong> $%s</p>', number_format($lot->min_offer, 2));
        
        if (!empty($lot->url)) {
            $description .= sprintf('<p><strong>Original Listing:</strong> <a href="%s" target="_blank">View on %s</a></p>', 
                esc_url($lot->url), $lot->auction_house);
        }
        
        // Add item details if available
        $items = USF_Database::get_lot_items($lot->lot_id);
        if (!empty($items)) {
            $description .= '<h4>Item Breakdown</h4>';
            $description .= '<table class="auction-items-table" style="width:100%; border-collapse: collapse;">';
            $description .= '<thead><tr style="background-color: #f5f5f5;">';
            $description .= '<th style="border: 1px solid #ddd; padding: 8px;">Quantity</th>';
            $description .= '<th style="border: 1px solid #ddd; padding: 8px;">Description</th>';
            $description .= '<th style="border: 1px solid #ddd; padding: 8px;">Grade</th>';
            if (!empty($items[0]->color)) {
                $description .= '<th style="border: 1px solid #ddd; padding: 8px;">Color</th>';
            }
            $description .= '</tr></thead><tbody>';
            
            foreach ($items as $item) {
                $description .= '<tr>';
                $description .= sprintf('<td style="border: 1px solid #ddd; padding: 8px;">%d</td>', $item->quantity);
                $description .= sprintf('<td style="border: 1px solid #ddd; padding: 8px;">%s</td>', $item->description);
                $description .= sprintf('<td style="border: 1px solid #ddd; padding: 8px;">%s</td>', $item->grade);
                if (!empty($item->color)) {
                    $description .= sprintf('<td style="border: 1px solid #ddd; padding: 8px;">%s</td>', $item->color);
                }
                $description .= '</tr>';
            }
            
            $description .= '</tbody></table>';
        }
        
        $description .= '<p><em>This is an auction lot purchase. All sales are final.</em></p>';
        
        return $description;
    }

    /**
     * Get first name from full name
     */
    private static function get_first_name($full_name) {
        $name_parts = explode(' ', trim($full_name));
        return $name_parts[0];
    }

    /**
     * Get last name from full name
     */
    private static function get_last_name($full_name) {
        $name_parts = explode(' ', trim($full_name));
        if (count($name_parts) > 1) {
            array_shift($name_parts);
            return implode(' ', $name_parts);
        }
        return '';
    }

    /**
     * Handle order status changes
     */
    public static function handle_order_status_change($order_id, $old_status, $new_status) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return;
        }

        // Check if this is an auction order
        $bid_id = $order->get_meta('_usf_auction_bid_id');
        if (!$bid_id) {
            return;
        }

        // Log status change
        $order_note = sprintf(
            'Auction order status changed from %s to %s',
            $old_status,
            $new_status
        );
        $order->add_order_note($order_note);

        // Handle specific status changes
        switch ($new_status) {
            case 'completed':
                self::handle_order_completion($order, $bid_id);
                break;
            case 'cancelled':
            case 'refunded':
                self::handle_order_cancellation($order, $bid_id);
                break;
        }
    }

    /**
     * Handle order completion
     */
    private static function handle_order_completion($order, $bid_id) {
        // Add completion note to bid
        global $wpdb;
        $table_bids = $wpdb->prefix . 'auction_bids';
        
        $completion_note = sprintf(
            'Payment completed. Order #%d processed successfully.',
            $order->get_id()
        );
        
        $wpdb->update(
            $table_bids,
            array('admin_notes' => $completion_note),
            array('id' => $bid_id),
            array('%s'),
            array('%d')
        );
    }

    /**
     * Handle order cancellation
     */
    private static function handle_order_cancellation($order, $bid_id) {
        // Optionally revert bid status or take other actions
        // This depends on business requirements
    }

    /**
     * Add custom order meta box in admin (HPOS compatible)
     */
    public static function add_order_meta_box($post_or_order_object) {
        add_meta_box(
            'usf_auction_order_details',
            'Auction Details',
            array(self::class, 'render_order_meta_box'),
            wc_get_container()->get(\Automattic\WooCommerce\Internal\DataStores\Orders\CustomOrdersTableController::class)->custom_orders_table_usage_is_enabled()
                ? wc_get_page_screen_id('shop-order')
                : 'shop_order',
            'side',
            'high'
        );
    }

    /**
     * Render order meta box (HPOS compatible)
     */
    public static function render_order_meta_box($post_or_order_object) {
        $order = ($post_or_order_object instanceof WP_Post) ? wc_get_order($post_or_order_object->ID) : $post_or_order_object;
        
        if (!$order) {
            return;
        }

        $lot_id = $order->get_meta('_usf_auction_lot_id');
        $bid_id = $order->get_meta('_usf_auction_bid_id');
        $auction_house = $order->get_meta('_usf_auction_house');
        $bid_amount = $order->get_meta('_usf_bid_amount');

        if (!$lot_id) {
            echo '<p>This is not an auction order.</p>';
            return;
        }

        echo '<table class="form-table">';
        echo '<tr><th>Lot ID:</th><td>' . esc_html($lot_id) . '</td></tr>';
        echo '<tr><th>Auction House:</th><td>' . esc_html($auction_house) . '</td></tr>';
        echo '<tr><th>Winning Bid:</th><td>$' . number_format($bid_amount, 2) . '</td></tr>';
        
        if ($bid_id) {
            $admin_url = admin_url('admin.php?page=usf-auction-bids&bid_id=' . $bid_id);
            echo '<tr><th>Bid Details:</th><td><a href="' . esc_url($admin_url) . '">View Bid #' . $bid_id . '</a></td></tr>';
        }
        
        echo '</table>';
    }

    /**
     * Create auction lots product category
     */
    public static function create_auction_category() {
        $category_exists = term_exists('auction-lots', 'product_cat');
        
        if (!$category_exists) {
            wp_insert_term(
                'Auction Lots',
                'product_cat',
                array(
                    'description' => 'Products created from auction lot purchases',
                    'slug' => 'auction-lots'
                )
            );
        }
    }

    /**
     * Get auction orders
     */
    public static function get_auction_orders($limit = 50, $offset = 0) {
        $orders = wc_get_orders(array(
            'limit' => $limit,
            'offset' => $offset,
            'meta_key' => '_usf_auction_lot_id',
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        return $orders;
    }

    /**
     * Get order statistics for auctions (HPOS compatible)
     */
    public static function get_order_stats() {
        $stats = array();

        // Use WooCommerce's HPOS-compatible method to get auction orders
        $auction_orders = wc_get_orders(array(
            'limit' => -1,
            'meta_key' => '_usf_auction_lot_id',
            'return' => 'ids'
        ));

        $stats['total_orders'] = count($auction_orders);

        if (!empty($auction_orders)) {
            $total_revenue = 0;
            $completed_orders = 0;
            $pending_orders = 0;

            foreach ($auction_orders as $order_id) {
                $order = wc_get_order($order_id);
                if (!$order) {
                    continue;
                }

                // Add to revenue
                $total_revenue += (float) $order->get_total();

                // Count by status
                $status = $order->get_status();
                if ($status === 'completed') {
                    $completed_orders++;
                } elseif ($status === 'pending') {
                    $pending_orders++;
                }
            }

            $stats['total_revenue'] = $total_revenue;
            $stats['completed_orders'] = $completed_orders;
            $stats['pending_orders'] = $pending_orders;
        } else {
            $stats['total_revenue'] = 0;
            $stats['completed_orders'] = 0;
            $stats['pending_orders'] = 0;
        }

        return $stats;
    }
}
