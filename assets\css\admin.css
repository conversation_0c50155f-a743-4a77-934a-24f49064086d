/**
 * Admin styles for USF Auction Plugin
 */

/* General Ad<PERSON> Styles */
.usf-admin-container {
    max-width: 1200px;
    margin: 20px 0;
}

.usf-admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.usf-admin-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

.usf-admin-actions {
    display: flex;
    gap: 10px;
}

/* Dashboard Styles */
.usf-dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.usf-stat-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.usf-stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #0073aa;
    display: block;
    margin-bottom: 5px;
}

.usf-stat-label {
    font-size: 14px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Upload Form Styles */
.usf-upload-container {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 30px;
    margin-bottom: 20px;
}

.usf-upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
}

.usf-upload-area:hover {
    border-color: #0073aa;
    background: #f0f8ff;
}

.usf-upload-area.dragover {
    border-color: #0073aa;
    background: #e6f3ff;
}

.usf-upload-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 15px;
}

.usf-upload-text {
    font-size: 16px;
    color: #666;
    margin-bottom: 15px;
}

.usf-file-input {
    margin-bottom: 20px;
}

.usf-upload-progress {
    display: none;
    margin-top: 20px;
}

.usf-progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
}

.usf-progress-fill {
    height: 100%;
    background: #0073aa;
    width: 0%;
    transition: width 0.3s ease;
}

/* Table Styles */
.usf-admin-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
}

.usf-admin-table th,
.usf-admin-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.usf-admin-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
}

.usf-admin-table tr:hover {
    background: #f8f9fa;
}

.usf-admin-table .usf-actions {
    white-space: nowrap;
}

.usf-admin-table .usf-actions .button {
    margin-right: 5px;
}

/* Status Badges */
.usf-status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.usf-status-active {
    background: #d4edda;
    color: #155724;
}

.usf-status-closed {
    background: #f8d7da;
    color: #721c24;
}

.usf-status-pending {
    background: #fff3cd;
    color: #856404;
}

.usf-status-accepted {
    background: #d1ecf1;
    color: #0c5460;
}

.usf-status-rejected {
    background: #f8d7da;
    color: #721c24;
}

/* Form Styles */
.usf-admin-form {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 30px;
}

.usf-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.usf-form-group {
    margin-bottom: 20px;
}

.usf-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.usf-form-group input,
.usf-form-group select,
.usf-form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.usf-form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.usf-form-group input:focus,
.usf-form-group select:focus,
.usf-form-group textarea:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.1);
}

/* Modal Styles */
.usf-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
}

.usf-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 8px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.usf-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.usf-modal-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.usf-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.usf-modal-close:hover {
    color: #333;
}

.usf-modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

/* Bulk Actions */
.usf-bulk-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.usf-bulk-actions select {
    min-width: 150px;
}

/* Pagination */
.usf-admin-pagination {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 20px;
}

.usf-admin-pagination .button {
    margin: 0;
}

/* Filters */
.usf-admin-filters {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.usf-admin-filters select,
.usf-admin-filters input {
    min-width: 150px;
}

/* Messages */
.usf-admin-message {
    padding: 12px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.usf-admin-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.usf-admin-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.usf-admin-message.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.usf-admin-message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Loading States */
.usf-loading {
    opacity: 0.6;
    pointer-events: none;
}

.usf-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .usf-dashboard-stats {
        grid-template-columns: 1fr;
    }
    
    .usf-form-row {
        grid-template-columns: 1fr;
    }
    
    .usf-admin-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .usf-bulk-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .usf-admin-table {
        font-size: 14px;
    }
    
    .usf-admin-table th,
    .usf-admin-table td {
        padding: 8px 10px;
    }
}

/* Settings Page */
.usf-settings-tabs {
    border-bottom: 1px solid #ddd;
    margin-bottom: 30px;
}

.usf-settings-tabs .nav-tab {
    margin-bottom: -1px;
}

.usf-settings-section {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 30px;
    margin-bottom: 20px;
}

.usf-settings-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
}

/* Email Template Editor */
.usf-email-template {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 20px;
}

.usf-email-template-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.usf-email-template-content {
    padding: 20px;
}

.usf-email-variables {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.usf-email-variables h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
}

.usf-variable-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.usf-variable-item {
    background: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 13px;
    border: 1px solid #ddd;
    cursor: pointer;
}

.usf-variable-item:hover {
    background: #e6f3ff;
    border-color: #0073aa;
}
