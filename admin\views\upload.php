<?php
/**
 * Admin CSV Upload View
 *
 * @package USF_Auction
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1>Upload CSV File</h1>
    
    <div class="usf-upload-container">
        <form id="usf-csv-upload-form" enctype="multipart/form-data">
            <?php wp_nonce_field('usf_upload_csv', 'upload_nonce'); ?>
            
            <div class="usf-form-field">
                <label for="auction_house"><strong>Select Auction House Format:</strong></label>
                <select id="auction_house" name="auction_house" required>
                    <option value="">-- Choose Auction House --</option>
                    <?php
                    // Get auction houses and create mapping for CSV parsing
                    $auction_houses = USF_Database::get_auction_houses(array('status' => 'active'));
                    $csv_mapping = array(
                        'AT&T' => 'att',
                        'Bell' => 'bell',
                        'T-Mobile' => 'tmobile'
                    );

                    foreach ($auction_houses as $house):
                        if (isset($csv_mapping[$house->name])):
                    ?>
                        <option value="<?php echo esc_attr($csv_mapping[$house->name]); ?>"><?php echo esc_html($house->name); ?></option>
                    <?php
                        endif;
                    endforeach;
                    ?>
                </select>
                <p class="description">Select which auction house format your CSV file uses.</p>
            </div>
            
            <div class="usf-upload-area">
                <input type="file" id="csv_file" name="csv_file" accept=".csv" required>
                <div class="usf-upload-text">
                    <p><strong>Choose CSV file or drag and drop</strong></p>
                    <p>Upload your auction CSV file</p>
                </div>
            </div>
            
            <div class="usf-upload-progress" style="display: none;">
                <div class="usf-progress-bar">
                    <div class="usf-progress-fill"></div>
                </div>
                <div class="usf-progress-text">Uploading...</div>
            </div>
            
            <div class="usf-upload-results" style="display: none;"></div>
            
            <button type="submit" class="button button-primary">Upload and Import</button>
        </form>
    </div>
    
    <div class="usf-format-info">
        <h2>Supported CSV Formats</h2>
        
        <h3>AT&T Format</h3>
        <p>Two-section format with lot headers and item details.</p>
        
        <h3>Bell Format</h3>
        <p>Two-section format with lot headers and item details.</p>
        
        <h3>T-Mobile Format</h3>
        <p>Two-section format with lot headers and item details.</p>

        <?php if (defined('WP_DEBUG') && WP_DEBUG): ?>
        <div class="usf-timezone-test" style="margin-top: 30px; padding: 15px; background: #f9f9f9; border-left: 4px solid #0073aa;">
            <h4>Timezone Conversion Test</h4>
            <p><strong>WordPress Timezone:</strong> <?php echo function_exists('wp_timezone') ? wp_timezone()->getName() : get_option('timezone_string', 'UTC'); ?></p>

            <?php
            // Test timezone conversion
            $test_results = USF_CSV_Parser::test_timezone_conversion();
            ?>

            <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                <thead>
                    <tr style="background: #e1e1e1;">
                        <th style="padding: 8px; border: 1px solid #ccc; text-align: left;">Original CSV Time</th>
                        <th style="padding: 8px; border: 1px solid #ccc; text-align: left;">Converted (WordPress TZ)</th>
                        <th style="padding: 8px; border: 1px solid #ccc; text-align: center;">Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($test_results as $result): ?>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ccc; font-family: monospace;"><?php echo esc_html($result['original']); ?></td>
                        <td style="padding: 8px; border: 1px solid #ccc; font-family: monospace;"><?php echo esc_html($result['converted'] ?: 'Failed'); ?></td>
                        <td style="padding: 8px; border: 1px solid #ccc; text-align: center;">
                            <?php if ($result['success']): ?>
                                <span style="color: green;">✓</span>
                            <?php else: ?>
                                <span style="color: red;">✗</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                <em>This test section is only visible when WP_DEBUG is enabled.</em>
            </p>
        </div>
        <?php endif; ?>
    </div>
</div>

<style>
.usf-upload-container {
    max-width: 600px;
    margin: 20px 0;
}

.usf-upload-area {
    border: 2px dashed #ccd0d4;
    border-radius: 4px;
    padding: 40px;
    text-align: center;
    margin-bottom: 20px;
    transition: border-color 0.3s;
}

.usf-upload-area:hover {
    border-color: #0073aa;
}

.usf-upload-area.dragover {
    border-color: #0073aa;
    background-color: #f0f8ff;
}

.usf-upload-text p {
    margin: 5px 0;
}

.usf-progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.usf-progress-fill {
    height: 100%;
    background-color: #0073aa;
    width: 0%;
    transition: width 0.3s;
}

.usf-format-info {
    margin-top: 40px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 4px;
}

.usf-format-info h3 {
    margin-top: 20px;
    margin-bottom: 10px;
}

.usf-form-field {
    margin-bottom: 20px;
}

.usf-form-field label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

.usf-form-field select {
    width: 100%;
    max-width: 300px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.usf-form-field .description {
    margin-top: 5px;
    font-style: italic;
    color: #666;
}

.usf-admin-message {
    padding: 12px;
    margin: 20px 0;
    border-left: 4px solid;
    border-radius: 4px;
}

.usf-admin-message.success {
    background-color: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.usf-admin-message.error {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.usf-import-summary {
    background: #f0f8ff;
    border: 1px solid #0073aa;
    border-radius: 4px;
    padding: 15px;
    margin-top: 20px;
}

.usf-import-summary ul {
    margin: 10px 0;
    padding-left: 20px;
}
</style>
