<?php

/**
 * Statistics helper functionality
 *
 * Centralized class for generating statistics to eliminate duplicate COUNT queries
 */

class USF_Statistics_Helper {

    /**
     * Get count of records by status
     */
    public static function get_count_by_status($table, $status, $additional_where = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . $table;
        $where_clause = "WHERE status = %s";
        $params = array($status);

        if (!empty($additional_where)) {
            $where_clause .= " AND " . $additional_where;
        }

        $sql = "SELECT COUNT(*) FROM $table_name $where_clause";
        
        return (int) $wpdb->get_var($wpdb->prepare($sql, $params));
    }

    /**
     * Get multiple counts by status for a table
     */
    public static function get_counts_by_statuses($table, $statuses, $additional_where = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . $table;
        $counts = array();

        foreach ($statuses as $status) {
            $counts[$status] = self::get_count_by_status($table, $status, $additional_where);
        }

        return $counts;
    }

    /**
     * Get total count for a table
     */
    public static function get_total_count($table, $where_clause = '', $params = array()) {
        global $wpdb;

        $table_name = $wpdb->prefix . $table;
        $sql = "SELECT COUNT(*) FROM $table_name";

        if (!empty($where_clause)) {
            $sql .= " WHERE " . $where_clause;
        }

        if (!empty($params)) {
            return (int) $wpdb->get_var($wpdb->prepare($sql, $params));
        }

        return (int) $wpdb->get_var($sql);
    }

    /**
     * Get sum of a column by status
     */
    public static function get_sum_by_status($table, $column, $status, $additional_where = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . $table;
        $where_clause = "WHERE status = %s";
        $params = array($status);

        if (!empty($additional_where)) {
            $where_clause .= " AND " . $additional_where;
        }

        $sql = "SELECT SUM($column) FROM $table_name $where_clause";
        
        return (float) $wpdb->get_var($wpdb->prepare($sql, $params));
    }

    /**
     * Get average of a column by status
     */
    public static function get_average_by_status($table, $column, $status, $additional_where = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . $table;
        $where_clause = "WHERE status = %s";
        $params = array($status);

        if (!empty($additional_where)) {
            $where_clause .= " AND " . $additional_where;
        }

        $sql = "SELECT AVG($column) FROM $table_name $where_clause";
        
        return (float) $wpdb->get_var($wpdb->prepare($sql, $params));
    }

    /**
     * Get maximum value of a column by status
     */
    public static function get_max_by_status($table, $column, $status, $additional_where = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . $table;
        $where_clause = "WHERE status = %s";
        $params = array($status);

        if (!empty($additional_where)) {
            $where_clause .= " AND " . $additional_where;
        }

        $sql = "SELECT MAX($column) FROM $table_name $where_clause";
        
        return (float) $wpdb->get_var($wpdb->prepare($sql, $params));
    }

    /**
     * Get counts grouped by a column
     */
    public static function get_counts_grouped_by($table, $group_column, $where_clause = '', $params = array()) {
        global $wpdb;

        $table_name = $wpdb->prefix . $table;
        $sql = "SELECT $group_column, COUNT(*) as count FROM $table_name";

        if (!empty($where_clause)) {
            $sql .= " WHERE " . $where_clause;
        }

        $sql .= " GROUP BY $group_column";

        if (!empty($params)) {
            $results = $wpdb->get_results($wpdb->prepare($sql, $params));
        } else {
            $results = $wpdb->get_results($sql);
        }

        $counts = array();
        foreach ($results as $result) {
            $counts[$result->$group_column] = (int) $result->count;
        }

        return $counts;
    }

    /**
     * Get count by date range
     */
    public static function get_count_by_date_range($table, $date_column, $start_date, $end_date = null, $additional_where = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . $table;
        $where_clause = "$date_column >= %s";
        $params = array($start_date);

        if ($end_date) {
            $where_clause .= " AND $date_column <= %s";
            $params[] = $end_date;
        }

        if (!empty($additional_where)) {
            $where_clause .= " AND " . $additional_where;
        }

        $sql = "SELECT COUNT(*) FROM $table_name WHERE $where_clause";
        
        return (int) $wpdb->get_var($wpdb->prepare($sql, $params));
    }

    /**
     * Get comprehensive auction statistics
     */
    public static function get_auction_statistics() {
        $stats = array();

        // Auction counts by status
        $auction_statuses = array('active', 'closed', 'cancelled');
        $auction_counts = self::get_counts_by_statuses('auction_lots', $auction_statuses);
        
        $stats['auctions'] = array(
            'total_active' => $auction_counts['active'],
            'total_closed' => $auction_counts['closed'],
            'total_cancelled' => $auction_counts['cancelled'],
            'total' => array_sum($auction_counts)
        );

        // Auction counts by auction house
        $stats['auctions']['by_auction_house'] = self::get_counts_grouped_by('auction_lots', 'auction_house');

        return $stats;
    }

    /**
     * Get comprehensive bid statistics
     */
    public static function get_bid_statistics() {
        $stats = array();

        // Bid counts by status
        $bid_statuses = array('pending', 'accepted', 'rejected');
        $bid_counts = self::get_counts_by_statuses('auction_bids', $bid_statuses);
        
        $stats['bids'] = array(
            'pending' => $bid_counts['pending'],
            'accepted' => $bid_counts['accepted'],
            'rejected' => $bid_counts['rejected'],
            'total' => array_sum($bid_counts)
        );

        // Revenue statistics
        $stats['revenue'] = array(
            'total_revenue' => self::get_sum_by_status('auction_bids', 'bid_amount', 'accepted'),
            'average_accepted_bid' => self::get_average_by_status('auction_bids', 'bid_amount', 'accepted'),
            'highest_bid' => self::get_max_by_status('auction_bids', 'bid_amount', 'accepted')
        );

        // Recent activity
        $today = current_time('Y-m-d');
        $week_ago = date('Y-m-d H:i:s', strtotime('-7 days'));
        
        $stats['activity'] = array(
            'bids_today' => self::get_count_by_date_range('auction_bids', 'DATE(bid_time)', $today),
            'bids_this_week' => self::get_count_by_date_range('auction_bids', 'bid_time', $week_ago)
        );

        return $stats;
    }

    /**
     * Get comprehensive dashboard statistics
     */
    public static function get_dashboard_statistics() {
        $auction_stats = self::get_auction_statistics();
        $bid_stats = self::get_bid_statistics();

        return array(
            'active_lots' => $auction_stats['auctions']['total_active'],
            'pending_bids' => $bid_stats['bids']['pending'],
            'accepted_bids' => $bid_stats['bids']['accepted'],
            'total_revenue' => $bid_stats['revenue']['total_revenue']
        );
    }
}
