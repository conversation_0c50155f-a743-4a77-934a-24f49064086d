<?php
/**
 * Plugin Name: Auction House Plugin
 * Plugin URI: https://phonescanada.com/usf-auction-plugin
 * Description: A comprehensive WordPress plugin for managing auction lots from multiple auction houses (AT&T, Bell, T-Mobile) with CSV import, bidding system, and WooCommerce integration.
 * Version: 1.0.0
 * Author: Phones Canada
 * Author URI: https://phonescanada.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: usf-auction
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.8.1
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 9.8.5
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Currently plugin version.
 */
define('USF_AUCTION_VERSION', '1.0.0');
define('USF_AUCTION_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('USF_AUCTION_PLUGIN_URL', plugin_dir_url(__FILE__));
define('USF_AUCTION_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * The code that runs during plugin activation.
 */
function activate_usf_auction() {
    require_once USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-activator.php';
    USF_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 */
function deactivate_usf_auction() {
    require_once USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-deactivator.php';
    USF_Deactivator::deactivate();
}

register_activation_hook(__FILE__, 'activate_usf_auction');
register_deactivation_hook(__FILE__, 'deactivate_usf_auction');

/**
 * Check if WooCommerce is active
 */
function usf_auction_check_woocommerce() {
    if (!class_exists('WooCommerce')) {
        add_action('admin_notices', 'usf_auction_woocommerce_missing_notice');
        return false;
    }
    return true;
}

/**
 * WooCommerce missing notice
 */
function usf_auction_woocommerce_missing_notice() {
    echo '<div class="error"><p><strong>' . esc_html__('USF Auction Plugin', 'usf-auction') . '</strong> ' . esc_html__('requires WooCommerce to be installed and active.', 'usf-auction') . '</p></div>';
}

/**
 * Declare WooCommerce HPOS compatibility
 */
add_action('before_woocommerce_init', function() {
    if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
        \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
        \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('cart_checkout_blocks', __FILE__, true);
    }
});

/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require USF_AUCTION_PLUGIN_DIR . 'includes/class-usf-auction-core.php';

/**
 * Begins execution of the plugin.
 */
function run_usf_auction() {
    // Check if WooCommerce is active
    if (!usf_auction_check_woocommerce()) {
        return;
    }
    
    $plugin = new USF_Auction_Core();
    $plugin->run();
}

// Initialize the plugin
add_action('plugins_loaded', 'run_usf_auction');
