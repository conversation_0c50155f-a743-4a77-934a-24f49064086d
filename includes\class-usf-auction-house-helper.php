<?php

/**
 * Auction House Helper functionality
 *
 * Provides utility functions for auction house management and display
 */

class USF_Auction_House_Helper {

    /**
     * Get auction house URL by name
     */
    public static function get_auction_house_url($auction_house_name) {
        // Get auction house by name
        $auction_house = USF_Database::get_auction_house_by_name($auction_house_name);
        
        if (!$auction_house || !$auction_house->assigned_page_id) {
            return null;
        }

        return get_permalink($auction_house->assigned_page_id);
    }

    /**
     * Get auction house URL by slug
     */
    public static function get_auction_house_url_by_slug($slug) {
        // Get auction house by slug
        $auction_house = USF_Database::get_auction_house_by_slug($slug);
        
        if (!$auction_house || !$auction_house->assigned_page_id) {
            return null;
        }

        return get_permalink($auction_house->assigned_page_id);
    }

    /**
     * Check if auction house has assigned page
     */
    public static function has_assigned_page($auction_house_name) {
        $auction_house = USF_Database::get_auction_house_by_name($auction_house_name);
        
        return $auction_house && !empty($auction_house->assigned_page_id);
    }

    /**
     * Generate clickable auction house link
     */
    public static function get_auction_house_link($auction_house_name, $css_class = 'usf-auction-house-link') {
        $url = self::get_auction_house_url($auction_house_name);
        
        if ($url) {
            return sprintf(
                '<a href="%s" class="%s" title="View %s information">%s</a>',
                esc_url($url),
                esc_attr($css_class),
                esc_attr($auction_house_name),
                esc_html($auction_house_name)
            );
        }
        
        // Return plain text if no URL available
        return sprintf(
            '<span class="usf-auction-house-name">%s</span>',
            esc_html($auction_house_name)
        );
    }

    /**
     * Get all active auction houses for dropdowns
     */
    public static function get_auction_houses_for_dropdown() {
        $auction_houses = USF_Database::get_auction_houses(array('status' => 'active'));
        
        $options = array();
        foreach ($auction_houses as $house) {
            $options[$house->name] = $house->name;
        }
        
        return $options;
    }

    /**
     * Get auction house slug by name
     */
    public static function get_auction_house_slug($auction_house_name) {
        $auction_house = USF_Database::get_auction_house_by_name($auction_house_name);
        
        return $auction_house ? $auction_house->slug : null;
    }

    /**
     * Generate slug from name
     */
    public static function generate_slug($name) {
        return sanitize_title($name);
    }

    /**
     * Validate auction house data
     */
    public static function validate_auction_house_data($data) {
        $errors = array();

        // Validate name
        if (empty($data['name'])) {
            $errors[] = 'Auction house name is required.';
        } elseif (strlen($data['name']) > 100) {
            $errors[] = 'Auction house name must be 100 characters or less.';
        }

        // Validate slug
        if (empty($data['slug'])) {
            $errors[] = 'Auction house slug is required.';
        } elseif (strlen($data['slug']) > 100) {
            $errors[] = 'Auction house slug must be 100 characters or less.';
        } elseif (!preg_match('/^[a-z0-9-]+$/', $data['slug'])) {
            $errors[] = 'Auction house slug can only contain lowercase letters, numbers, and hyphens.';
        }

        // Check for duplicate name (excluding current record if updating)
        $existing_by_name = USF_Database::get_auction_house_by_name($data['name']);
        if ($existing_by_name && (!isset($data['id']) || $existing_by_name->id != $data['id'])) {
            $errors[] = 'An auction house with this name already exists.';
        }

        // Check for duplicate slug (excluding current record if updating)
        $existing_by_slug = USF_Database::get_auction_house_by_slug($data['slug']);
        if ($existing_by_slug && (!isset($data['id']) || $existing_by_slug->id != $data['id'])) {
            $errors[] = 'An auction house with this slug already exists.';
        }

        // Validate page assignment
        if (!empty($data['assigned_page_id'])) {
            $page = get_post($data['assigned_page_id']);
            if (!$page || $page->post_type !== 'page' || $page->post_status !== 'publish') {
                $errors[] = 'Selected page is not valid or not published.';
            }
        }

        return array(
            'valid' => empty($errors),
            'errors' => $errors
        );
    }

    /**
     * Get auction house statistics
     */
    public static function get_auction_house_stats($auction_house_name) {
        global $wpdb;

        $table_lots = $wpdb->prefix . 'auction_lots';
        $table_bids = $wpdb->prefix . 'auction_bids';

        // Get total lots
        $total_lots = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_lots WHERE auction_house = %s",
            $auction_house_name
        ));

        // Get active lots
        $active_lots = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_lots WHERE auction_house = %s AND status = 'active' AND closing_time > NOW()",
            $auction_house_name
        ));

        // Get total bids for this auction house
        $total_bids = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_bids b 
             INNER JOIN $table_lots l ON b.lot_id = l.lot_id 
             WHERE l.auction_house = %s",
            $auction_house_name
        ));

        return array(
            'total_lots' => (int) $total_lots,
            'active_lots' => (int) $active_lots,
            'total_bids' => (int) $total_bids
        );
    }

    /**
     * Migrate existing auction house data
     */
    public static function migrate_existing_auction_houses() {
        global $wpdb;

        $table_lots = $wpdb->prefix . 'auction_lots';
        
        // Get unique auction house names from existing lots
        $existing_houses = $wpdb->get_col(
            "SELECT DISTINCT auction_house FROM $table_lots WHERE auction_house IS NOT NULL AND auction_house != ''"
        );

        foreach ($existing_houses as $house_name) {
            // Check if this auction house already exists in the new table
            $existing = USF_Database::get_auction_house_by_name($house_name);
            
            if (!$existing) {
                // Create new auction house record
                $data = array(
                    'name' => $house_name,
                    'slug' => self::generate_slug($house_name),
                    'description' => sprintf('%s Auction House - Quality telecommunications equipment and devices.', $house_name),
                    'status' => 'active'
                );
                
                USF_Database::save_auction_house($data);
            }
        }
    }
}
