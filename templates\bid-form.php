<?php
/**
 * Template for bid submission form
 *
 * @package USF_Auction
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if user is logged in
if (!is_user_logged_in()) {
?>
<div class="usf-bid-form-container">
    <h3>Login Required</h3>
    <p>You must be logged in to place a bid on this auction.</p>
    <div class="usf-login-actions">
        <a href="<?php echo esc_url(wp_login_url(get_permalink())); ?>" class="usf-btn usf-btn-primary">Login</a>
        <a href="<?php echo esc_url(wp_registration_url()); ?>" class="usf-btn usf-btn-secondary">Register</a>
    </div>
</div>
<?php
    return;
}

// Get current user data
$current_user = wp_get_current_user();

// Check user's bid status for this lot
$user_bid_status = USF_Database::get_user_bid_status($auction->lot_id, $current_user->ID);
$can_bid = USF_Database::user_can_bid($auction->lot_id, $current_user->ID);

// Calculate minimum bid requirement considering rejected bids
$minimum_bid_amount = $auction->min_offer;
$minimum_bid_reason = 'starting price';
$user_pending_bid = null;
$user_rejected_bid = null;

// Debug information (remove this in production)
$debug_info = array(
    'auction_min_offer' => $auction->min_offer,
    'user_id' => $current_user->ID,
    'lot_id' => $auction->lot_id
);

if ($current_user->ID) {
    // Check for pending bid first
    $user_pending_bid = USF_Database::get_user_pending_bid_amount($auction->lot_id, $current_user->ID);
    $debug_info['user_pending_bid'] = $user_pending_bid;

    if ($user_pending_bid) {
        $minimum_bid_amount = $user_pending_bid + 0.01;
        $minimum_bid_reason = 'current bid';
        $debug_info['calculation'] = 'Used pending bid: ' . $user_pending_bid . ' + 0.01 = ' . $minimum_bid_amount;
    } else {
        // Check for rejected bids
        $user_rejected_bid = USF_Database::get_user_highest_rejected_bid_amount($auction->lot_id, $current_user->ID);
        $debug_info['user_rejected_bid'] = $user_rejected_bid;

        if ($user_rejected_bid) {
            // For rejected bids, minimum must exceed the rejected amount (not revert to starting price)
            $minimum_bid_amount = $user_rejected_bid + 0.01;
            $minimum_bid_reason = 'rejected bid';
            $debug_info['calculation'] = 'Used rejected bid: ' . $user_rejected_bid . ' + 0.01 = ' . $minimum_bid_amount;
        } else {
            $debug_info['calculation'] = 'Used starting price: ' . $minimum_bid_amount;
        }
    }
}

// Debug output (remove this in production)
if (current_user_can('administrator') && isset($_GET['debug'])) {
    echo '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">';
    echo '<h4>Debug Information:</h4>';
    echo '<pre>' . print_r($debug_info, true) . '</pre>';
    echo '<p><strong>Final minimum_bid_amount:</strong> $' . number_format($minimum_bid_amount, 2) . '</p>';
    echo '<p><strong>Final minimum_bid_reason:</strong> ' . $minimum_bid_reason . '</p>';

    // Additional debug for bid status
    if ($user_bid_status) {
        echo '<h5>User Bid Status:</h5>';
        echo '<pre>' . print_r($user_bid_status, true) . '</pre>';
        echo '<p><strong>Status:</strong> "' . $user_bid_status->status . '"</p>';
        echo '<p><strong>Status === "rejected":</strong> ' . ($user_bid_status->status === 'rejected' ? 'TRUE' : 'FALSE') . '</p>';
    } else {
        echo '<p><strong>User Bid Status:</strong> NULL (no existing bid)</p>';
    }

    echo '</div>';
}

// Check if auction is still active
$closing_time = strtotime($auction->closing_time);
$now = current_time('timestamp');
$is_auction_active = $closing_time > $now;
?>

<div class="usf-bid-form-container">

    <?php if ($user_bid_status): ?>
        <?php if ($user_bid_status->status === 'pending'): ?>
            <!-- User has pending bid -->
            <h3>Active Bid</h3>
            <div class="usf-bid-status usf-bid-pending">
                <div class="usf-status-icon">⏳</div>
                <div class="usf-status-content">
                    <p><strong>Your bid is under review</strong></p>
                    <div class="usf-bid-details">
                        <p><strong>Current Bid Amount:</strong> $<?php echo number_format($user_bid_status->bid_amount, 2); ?></p>
                        <p><strong>Submitted:</strong> <?php echo date('M j, Y g:i A', strtotime($user_bid_status->bid_time)); ?></p>
                    </div>
                    <p>You will receive an email notification once a decision has been made on your bid.</p>
                </div>
            </div>

            <?php if ($is_auction_active): ?>
                <!-- Allow user to place a higher bid -->
                <div class="usf-higher-bid-section">
                    <h4>Place a Higher Bid</h4>
                    <p>You can submit a higher bid amount to increase your chances of winning this auction.</p>

                    <form class="usf-bid-form" data-lot-id="<?php echo esc_attr($auction->lot_id); ?>" data-current-bid="<?php echo esc_attr($user_bid_status->bid_amount); ?>">
                        <?php wp_nonce_field('usf_submit_bid', 'usf_bid_nonce'); ?>

                        <div class="usf-form-group">
                            <label for="bid_amount">New Bid Amount * (Must be higher than $<?php echo number_format($user_bid_status->bid_amount, 2); ?>)</label>
                            <input type="number"
                                   id="bid_amount"
                                   name="bid_amount"
                                   min="<?php echo esc_attr($user_bid_status->bid_amount + 0.01); ?>"
                                   step="0.01"
                                   placeholder="<?php echo esc_attr($user_bid_status->bid_amount + 1); ?>"
                                   required>
                            <small>Enter a bid amount higher than your current bid of $<?php echo number_format($user_bid_status->bid_amount, 2); ?>.</small>
                        </div>

                        <div class="usf-form-group">
                            <label class="usf-checkbox-label">
                                <input type="checkbox" id="terms_accepted" name="terms_accepted" required>
                                I agree to the <a href="#usf-terms" onclick="document.querySelector('.usf-auction-terms').scrollIntoView()">terms and conditions</a> *
                            </label>
                        </div>

                        <button type="submit" class="usf-btn usf-btn-primary usf-btn-large">
                            Submit Higher Bid
                        </button>

                        <div class="usf-bid-messages"></div>
                    </form>
                </div>
            <?php endif; ?>
            
        <?php elseif ($user_bid_status->status === 'accepted'): ?>
            <!-- User's bid was accepted -->
            <h3>Congratulations!</h3>
            <div class="usf-bid-status usf-bid-accepted">
                <div class="usf-status-icon">🎉</div>
                <div class="usf-status-content">
                    <p><strong>Your bid has been accepted!</strong></p>
                    <div class="usf-bid-details">
                        <p><strong>Winning Bid:</strong> $<?php echo number_format($user_bid_status->bid_amount, 2); ?></p>
                        <p><strong>Accepted:</strong> <?php echo date('M j, Y g:i A', strtotime($user_bid_status->bid_time)); ?></p>
                    </div>
                    <?php if ($user_bid_status->woocommerce_order_id): ?>
                        <p><a href="<?php echo esc_url(wc_get_order_url($user_bid_status->woocommerce_order_id)); ?>" class="usf-btn usf-btn-primary">Complete Payment</a></p>
                    <?php endif; ?>
                </div>
            </div>
            
        <?php elseif ($user_bid_status->status === 'rejected'): ?>
            <!-- User's bid was rejected - can bid again if auction is active -->
            <?php
            // For rejected bids, ensure we're using the highest rejected bid amount for validation
            $highest_rejected_bid = USF_Database::get_user_highest_rejected_bid_amount($auction->lot_id, $current_user->ID);
            $displayed_rejected_bid = $highest_rejected_bid ?: $user_bid_status->bid_amount;

            // Recalculate minimum bid amount specifically for this rejected bid scenario
            // For rejected bids, minimum must exceed the rejected amount (not revert to starting price)
            $rejected_minimum_bid_amount = $displayed_rejected_bid + 0.01;
            ?>
            <h3>Bid Not Accepted</h3>
            <div class="usf-bid-status usf-bid-rejected">
                <div class="usf-status-icon">❌</div>
                <div class="usf-status-content">
                    <p><strong>Your previous bid was not accepted</strong></p>
                    <div class="usf-bid-details">
                        <p><strong>Previous Bid:</strong> $<?php echo number_format($user_bid_status->bid_amount, 2); ?></p>
                        <?php if ($highest_rejected_bid && $highest_rejected_bid != $user_bid_status->bid_amount): ?>
                            <p><strong>Highest Rejected Bid:</strong> $<?php echo number_format($highest_rejected_bid, 2); ?></p>
                        <?php endif; ?>
                        <?php if (!empty($user_bid_status->admin_notes)): ?>
                            <p><strong>Note:</strong> <?php echo esc_html($user_bid_status->admin_notes); ?></p>
                        <?php endif; ?>
                    </div>
                    <?php if ($is_auction_active): ?>
                        <p><strong>You can submit a new bid below.</strong></p>
                    <?php else: ?>
                        <p>This auction has ended and is no longer accepting bids.</p>
                    <?php endif; ?>
                </div>
            </div>

            <?php if ($is_auction_active): ?>
                <!-- Show bid form for rejected bids if auction is still active -->
                <div class="usf-new-bid-section">
                    <h4>Submit New Bid</h4>
                    <form class="usf-bid-form" data-lot-id="<?php echo esc_attr($auction->lot_id); ?>">
                        <?php wp_nonce_field('usf_submit_bid', 'usf_bid_nonce'); ?>

                        <div class="usf-form-group">
                            <?php
                            $label_text = 'Bid Amount * (Minimum: $' . number_format($rejected_minimum_bid_amount, 2) . ')';
                            $help_text = 'Enter your bid amount in USD. Must be higher than your previous rejected bid of $' . number_format($displayed_rejected_bid, 2) . '.';
                            ?>
                            <label for="bid_amount"><?php echo esc_html($label_text); ?></label>
                            <input type="number"
                                   id="bid_amount"
                                   name="bid_amount"
                                   min="<?php echo esc_attr($rejected_minimum_bid_amount); ?>"
                                   step="0.01"
                                   placeholder="<?php echo esc_attr($rejected_minimum_bid_amount); ?>"
                                   required>
                            <small><?php echo esc_html($help_text); ?></small>
                        </div>
                        
                        <div class="usf-form-group">
                            <label class="usf-checkbox-label">
                                <input type="checkbox" id="terms_accepted" name="terms_accepted" required>
                                I agree to the <a href="#usf-terms" onclick="document.querySelector('.usf-auction-terms').scrollIntoView()">terms and conditions</a> *
                            </label>
                        </div>
                        
                        <button type="submit" class="usf-btn usf-btn-primary usf-btn-large">
                            Submit New Bid
                        </button>
                        
                        <div class="usf-bid-messages"></div>
                    </form>
                </div>
            <?php endif; ?>
        <?php endif; ?>
        
    <?php else: ?>
        <!-- User has no bid - show normal bid form -->
        <?php if ($is_auction_active): ?>
            <h3>Place Your Bid</h3>
            
            <form class="usf-bid-form" data-lot-id="<?php echo esc_attr($auction->lot_id); ?>">
                <?php wp_nonce_field('usf_submit_bid', 'usf_bid_nonce'); ?>
                
                <div class="usf-form-group">
                    <?php
                    $label_text = 'Bid Amount * (Minimum: $' . number_format($minimum_bid_amount, 2) . ')';
                    $help_text = 'Enter your bid amount in USD. ';

                    if ($minimum_bid_reason === 'rejected bid') {
                        $help_text .= 'Must be higher than your previous rejected bid of $' . number_format($user_rejected_bid, 2) . '.';
                    } elseif ($minimum_bid_reason === 'current bid') {
                        $help_text .= 'Must be higher than your current bid of $' . number_format($user_pending_bid, 2) . '.';
                    } else {
                        $help_text .= 'Must be at least $' . number_format($minimum_bid_amount, 2) . '.';
                    }
                    ?>
                    <label for="bid_amount"><?php echo esc_html($label_text); ?></label>
                    <input type="number"
                           id="bid_amount"
                           name="bid_amount"
                           min="<?php echo esc_attr($minimum_bid_amount); ?>"
                           step="0.01"
                           placeholder="<?php echo esc_attr($minimum_bid_amount); ?>"
                           required>
                    <small><?php echo esc_html($help_text); ?></small>
                </div>
                
                <div class="usf-form-group">
                    <label class="usf-checkbox-label">
                        <input type="checkbox" id="terms_accepted" name="terms_accepted" required>
                        I agree to the <a href="#usf-terms" onclick="document.querySelector('.usf-auction-terms').scrollIntoView()">terms and conditions</a> *
                    </label>
                </div>
                
                <button type="submit" class="usf-btn usf-btn-primary usf-btn-large">
                    Submit Bid
                </button>
                
                <div class="usf-bid-messages"></div>
            </form>
        <?php else: ?>
            <h3>Auction Closed</h3>
            <p>This auction has ended and is no longer accepting bids.</p>
        <?php endif; ?>
    <?php endif; ?>
    
    <!-- Bid Information -->
    <div class="usf-bid-info">
        <h4>Important Information</h4>
        <ul>
            <li><strong>Binding Agreement:</strong> Your bid is a binding offer to purchase.</li>
            <li><strong>Payment:</strong> Payment must be completed within 48 hours if your bid is accepted.</li>
            <li><strong>Inspection:</strong> Items are sold as-is. Please review the item details carefully.</li>
            <li><strong>Updates:</strong> You'll receive email notifications about your bid status.</li>
            <?php if ($user_bid_status && $user_bid_status->status === 'rejected' && $is_auction_active): ?>
                <li><strong>Re-bidding:</strong> You can submit a new bid since your previous bid was not accepted.</li>
            <?php endif; ?>
        </ul>
    </div>
    
    <!-- Contact Information -->
    <div class="usf-contact-info">
        <h4>Questions?</h4>
        <p>Contact us for more information about this auction:</p>
        <ul>
            <?php 
            $contact_email = get_option('usf_contact_email', get_option('admin_email'));
            $contact_phone = get_option('usf_contact_phone', '');
            ?>
            <li><strong>Email:</strong> <a href="mailto:<?php echo esc_attr($contact_email); ?>"><?php echo esc_html($contact_email); ?></a></li>
            <?php if ($contact_phone): ?>
                <li><strong>Phone:</strong> <a href="tel:<?php echo esc_attr($contact_phone); ?>"><?php echo esc_html($contact_phone); ?></a></li>
            <?php endif; ?>
        </ul>
    </div>
</div>
