<?php
/**
 * Admin Settings View
 *
 * @package USF_Auction
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle form submission
if (isset($_POST['submit']) && wp_verify_nonce($_POST['settings_nonce'], 'usf_settings_save')) {
    // Save email templates
    $email_templates = array(
        'bid_confirmation_subject' => sanitize_text_field($_POST['bid_confirmation_subject']),
        'bid_confirmation_body' => sanitize_textarea_field($_POST['bid_confirmation_body']),
        'admin_notification_subject' => sanitize_text_field($_POST['admin_notification_subject']),
        'admin_notification_body' => sanitize_textarea_field($_POST['admin_notification_body']),
        'bid_acceptance_subject' => sanitize_text_field($_POST['bid_acceptance_subject']),
        'bid_acceptance_body' => sanitize_textarea_field($_POST['bid_acceptance_body']),
        'bid_rejection_subject' => sanitize_text_field($_POST['bid_rejection_subject']),
        'bid_rejection_body' => sanitize_textarea_field($_POST['bid_rejection_body'])
    );
    
    foreach ($email_templates as $key => $value) {
        USF_Database::update_setting('email_' . $key, $value);
    }
    
    // Save general settings
    USF_Database::update_setting('admin_email', sanitize_email($_POST['admin_email']));
    USF_Database::update_setting('company_name', sanitize_text_field($_POST['company_name']));
    USF_Database::update_setting('contact_phone', sanitize_text_field($_POST['contact_phone']));
    USF_Database::update_setting('contact_address', sanitize_textarea_field($_POST['contact_address']));
    
    // Save page settings
    if (isset($_POST['single_auction_page'])) {
        USF_Database::update_setting('single_auction_page', intval($_POST['single_auction_page']));
    }
    
    echo '<div class="notice notice-success"><p>Settings saved successfully!</p></div>';
}

// Get current settings
$settings = array(
    'bid_confirmation_subject' => USF_Database::get_setting('email_bid_confirmation_subject', 'Bid submitted for Lot #{lot_id}'),
    'bid_confirmation_body' => USF_Database::get_setting('email_bid_confirmation_body', ''),
    'admin_notification_subject' => USF_Database::get_setting('email_admin_notification_subject', 'New bid received for Lot #{lot_id}'),
    'admin_notification_body' => USF_Database::get_setting('email_admin_notification_body', ''),
    'bid_acceptance_subject' => USF_Database::get_setting('email_bid_acceptance_subject', 'Congratulations! Your bid was accepted for Lot #{lot_id}'),
    'bid_acceptance_body' => USF_Database::get_setting('email_bid_acceptance_body', ''),
    'bid_rejection_subject' => USF_Database::get_setting('email_bid_rejection_subject', 'Bid not accepted for Lot #{lot_id}'),
    'bid_rejection_body' => USF_Database::get_setting('email_bid_rejection_body', ''),
    'admin_email' => USF_Database::get_setting('admin_email', get_option('admin_email')),
    'company_name' => USF_Database::get_setting('company_name', get_bloginfo('name')),
    'contact_phone' => USF_Database::get_setting('contact_phone', ''),
    'contact_address' => USF_Database::get_setting('contact_address', '')
);
?>

<div class="wrap">
    <h1>USF Auction Settings</h1>
    
    <form method="post" action="">
        <?php wp_nonce_field('usf_settings_save', 'settings_nonce'); ?>
        
        <div class="usf-settings-tabs">
            <nav class="nav-tab-wrapper">
                <a href="#email-templates" class="nav-tab nav-tab-active">Email Templates</a>
                <a href="#general-settings" class="nav-tab">General Settings</a>
                <a href="#page-settings" class="nav-tab">Pages</a>
                <a href="#template-variables" class="nav-tab">Template Variables</a>
            </nav>
            
            <!-- Email Templates Tab -->
            <div id="email-templates" class="usf-tab-content">
                <h2>Email Templates</h2>
                
                <h3>Bid Confirmation Email (to bidder)</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">Subject</th>
                        <td>
                            <input type="text" name="bid_confirmation_subject" value="<?php echo esc_attr($settings['bid_confirmation_subject']); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Body</th>
                        <td>
                            <textarea name="bid_confirmation_body" rows="8" cols="50" class="large-text"><?php echo esc_textarea($settings['bid_confirmation_body']); ?></textarea>
                        </td>
                    </tr>
                </table>
                
                <h3>Admin Notification Email</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">Subject</th>
                        <td>
                            <input type="text" name="admin_notification_subject" value="<?php echo esc_attr($settings['admin_notification_subject']); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Body</th>
                        <td>
                            <textarea name="admin_notification_body" rows="8" cols="50" class="large-text"><?php echo esc_textarea($settings['admin_notification_body']); ?></textarea>
                        </td>
                    </tr>
                </table>
                
                <h3>Bid Acceptance Email (to bidder)</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">Subject</th>
                        <td>
                            <input type="text" name="bid_acceptance_subject" value="<?php echo esc_attr($settings['bid_acceptance_subject']); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Body</th>
                        <td>
                            <textarea name="bid_acceptance_body" rows="8" cols="50" class="large-text"><?php echo esc_textarea($settings['bid_acceptance_body']); ?></textarea>
                        </td>
                    </tr>
                </table>
                
                <h3>Bid Rejection Email (to bidder)</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">Subject</th>
                        <td>
                            <input type="text" name="bid_rejection_subject" value="<?php echo esc_attr($settings['bid_rejection_subject']); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Body</th>
                        <td>
                            <textarea name="bid_rejection_body" rows="8" cols="50" class="large-text"><?php echo esc_textarea($settings['bid_rejection_body']); ?></textarea>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- General Settings Tab -->
            <div id="general-settings" class="usf-tab-content" style="display: none;">
                <h2>General Settings</h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">Admin Email</th>
                        <td>
                            <input type="email" name="admin_email" value="<?php echo esc_attr($settings['admin_email']); ?>" class="regular-text" />
                            <p class="description">Email address for admin notifications</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Company Name</th>
                        <td>
                            <input type="text" name="company_name" value="<?php echo esc_attr($settings['company_name']); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Contact Phone</th>
                        <td>
                            <input type="text" name="contact_phone" value="<?php echo esc_attr($settings['contact_phone']); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Contact Address</th>
                        <td>
                            <textarea name="contact_address" rows="4" cols="50" class="large-text"><?php echo esc_textarea($settings['contact_address']); ?></textarea>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Page Settings Tab -->
            <div id="page-settings" class="usf-tab-content" style="display: none;">
                <h2>Page Settings</h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">Single Auction Page</th>
                        <td>
                            <?php
                            $current_page = USF_Database::get_setting('single_auction_page', '');
                            $pages = get_pages(array(
                                'sort_column' => 'post_title',
                                'sort_order' => 'ASC',
                                'post_status' => 'publish'
                            ));
                            ?>
                            <select name="single_auction_page" class="regular-text">
                                <option value="">Select a page...</option>
                                <?php foreach ($pages as $page): ?>
                                    <option value="<?php echo esc_attr($page->ID); ?>" 
                                            <?php selected($current_page, $page->ID); ?>>
                                        <?php echo esc_html($page->post_title); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description">
                                Select the page where single auction details should be displayed. 
                                Make sure to add the <code>[singleauction]</code> shortcode to this page.
                                <br><br>
                                <strong>Setup Instructions:</strong><br>
                                1. Create a new page (e.g., "Auction Details")<br>
                                2. Add the <code>[singleauction]</code> shortcode to the page content<br>
                                3. Publish the page<br>
                                4. Select it from the dropdown above<br>
                                5. Save settings
                            </p>
                            
                            <?php if (!empty($current_page)): ?>
                                <?php $selected_page = get_post($current_page); ?>
                                <?php if ($selected_page): ?>
                                    <div class="usf-page-info">
                                        <strong>Current Page:</strong> 
                                        <a href="<?php echo esc_url(get_permalink($current_page)); ?>" target="_blank">
                                            <?php echo esc_html($selected_page->post_title); ?>
                                        </a>
                                        <span class="dashicons dashicons-external"></span>
                                        <br>
                                        <a href="<?php echo esc_url(get_edit_post_link($current_page)); ?>" target="_blank">
                                            Edit this page
                                        </a>
                                        <span class="dashicons dashicons-edit"></span>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Template Variables Tab -->
            <div id="template-variables" class="usf-tab-content" style="display: none;">
                <h2>Available Template Variables</h2>
                
                <p>You can use the following variables in your email templates:</p>
                
                <div class="usf-variables-grid">
                    <div class="usf-variable-group">
                        <h3>Lot Information</h3>
                        <ul>
                            <li><code>{lot_id}</code> - Lot ID</li>
                            <li><code>{model}</code> - Device model</li>
                            <li><code>{grade}</code> - Item grade</li>
                            <li><code>{auction_house}</code> - Auction house name</li>
                            <li><code>{min_offer}</code> - Minimum offer amount</li>
                            <li><code>{closing_time}</code> - Auction closing time</li>
                        </ul>
                    </div>
                    
                    <div class="usf-variable-group">
                        <h3>Bidder Information</h3>
                        <ul>
                            <li><code>{user_name}</code> - Bidder's name</li>
                            <li><code>{user_email}</code> - Bidder's email</li>
                            <li><code>{user_phone}</code> - Bidder's phone</li>
                            <li><code>{bid_amount}</code> - Bid amount</li>
                        </ul>
                    </div>
                    
                    <div class="usf-variable-group">
                        <h3>Admin Information</h3>
                        <ul>
                            <li><code>{admin_notes}</code> - Admin notes</li>
                            <li><code>{order_id}</code> - WooCommerce order ID</li>
                            <li><code>{order_link}</code> - Link to order</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <?php submit_button(); ?>
    </form>
    
    <div class="usf-test-email-section">
        <h2>Test Email</h2>
        <p>Send a test email to verify your templates are working correctly.</p>
        
        <div class="usf-test-email-form">
            <select id="test-email-type">
                <option value="bid_confirmation">Bid Confirmation</option>
                <option value="admin_notification">Admin Notification</option>
                <option value="bid_acceptance">Bid Acceptance</option>
                <option value="bid_rejection">Bid Rejection</option>
            </select>
            <input type="email" id="test-email-recipient" placeholder="<EMAIL>" />
            <button type="button" class="button" onclick="sendTestEmail()">Send Test Email</button>
        </div>
    </div>
</div>

<style>
.usf-settings-tabs {
    margin-top: 20px;
}

.usf-tab-content {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-top: none;
    padding: 20px;
}

.usf-variables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.usf-variable-group {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
}

.usf-variable-group h3 {
    margin-top: 0;
    margin-bottom: 10px;
}

.usf-variable-group ul {
    margin: 0;
    padding-left: 20px;
}

.usf-variable-group code {
    background: #fff;
    padding: 2px 4px;
    border-radius: 2px;
    font-family: monospace;
}

.usf-test-email-section {
    margin-top: 40px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 4px;
}

.usf-test-email-form {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-top: 10px;
}

.usf-test-email-form select,
.usf-test-email-form input {
    padding: 5px 10px;
}

.usf-page-info {
    margin-top: 15px;
    padding: 10px;
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
}

.usf-page-info a {
    text-decoration: none;
}

.usf-page-info .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    margin-left: 5px;
}
</style>

<script>
// Tab functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('.nav-tab');
    const contents = document.querySelectorAll('.usf-tab-content');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all tabs
            tabs.forEach(t => t.classList.remove('nav-tab-active'));
            
            // Hide all content
            contents.forEach(c => c.style.display = 'none');
            
            // Add active class to clicked tab
            this.classList.add('nav-tab-active');
            
            // Show corresponding content
            const target = this.getAttribute('href').substring(1);
            document.getElementById(target).style.display = 'block';
        });
    });
});

function sendTestEmail() {
    const emailType = document.getElementById('test-email-type').value;
    const recipient = document.getElementById('test-email-recipient').value;
    
    if (!recipient) {
        alert('Please enter a recipient email address.');
        return;
    }
    
    // Implementation for sending test email
    alert(`Sending test ${emailType} email to ${recipient}`);
}
</script>
