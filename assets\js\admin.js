/**
 * Admin JavaScript for USF Auction Plugin
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initUploadForm();
        initBidActions();
        initBulkActions();
        initModals();
        initFilters();
        initEmailTemplates();
    });

    /**
     * Initialize CSV upload form
     */
    function initUploadForm() {
        var $uploadArea = $('.usf-upload-area');
        var $fileInput = $('#csv_file');
        var $uploadForm = $('#usf-csv-upload-form');

        // Drag and drop functionality
        $uploadArea.on('dragover dragenter', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).addClass('dragover');
        });

        $uploadArea.on('dragleave dragend', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('dragover');
        });

        $uploadArea.on('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('dragover');

            var files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                $fileInput[0].files = files;
                $fileInput.trigger('change');
            }
        });

        // File input change
        $fileInput.on('change', function() {
            var file = this.files[0];
            if (file) {
                $('.usf-upload-text').text('Selected: ' + file.name);
            }
        });

        // Form submission
        $uploadForm.on('submit', function(e) {
            e.preventDefault();

            var formData = new FormData();
            var file = $fileInput[0].files[0];
            var auctionHouse = $('#auction_house').val();

            if (!auctionHouse) {
                showAdminMessage('error', 'Please select an auction house format.');
                return;
            }

            if (!file) {
                showAdminMessage('error', 'Please select a CSV file to upload.');
                return;
            }

            if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
                showAdminMessage('error', 'Please select a valid CSV file.');
                return;
            }

            formData.append('csv_file', file);
            formData.append('auction_house', auctionHouse);
            formData.append('action', 'usf_upload_csv');
            formData.append('nonce', usf_admin_ajax.upload_nonce);

            // Show progress
            var $progress = $('.usf-upload-progress');
            var $progressFill = $('.usf-progress-fill');
            var $submitBtn = $uploadForm.find('button[type="submit"]');

            $progress.show();
            $progressFill.css('width', '0%');
            $submitBtn.prop('disabled', true).text('Uploading...');

            // Upload file
            $.ajax({
                url: usf_admin_ajax.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function() {
                    var xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener('progress', function(e) {
                        if (e.lengthComputable) {
                            var percentComplete = (e.loaded / e.total) * 100;
                            $progressFill.css('width', percentComplete + '%');
                        }
                    });
                    return xhr;
                },
                success: function(response) {
                    $progressFill.css('width', '100%');
                    
                    if (response.success) {
                        showAdminMessage('success', response.data.message);
                        
                        // Show import summary
                        var summary = '<p><strong>Import Summary:</strong></p>';
                        summary += '<ul>';
                        summary += '<li>Total lots imported: ' + response.data.imported_count + '</li>';
                        summary += '<li>Total lots processed: ' + response.data.total_lots + '</li>';
                        if (response.data.errors && response.data.errors.length > 0) {
                            summary += '<li>Errors: ' + response.data.errors.length + '</li>';
                        }
                        summary += '</ul>';
                        
                        $('.usf-upload-container').append('<div class="usf-import-summary">' + summary + '</div>');
                        
                        // Reset form
                        $uploadForm[0].reset();
                        $('.usf-upload-text').html('<p><strong>Choose CSV file or drag and drop</strong></p><p>Upload your auction CSV file</p>');
                        
                    } else {
                        showAdminMessage('error', response.data.message);
                    }
                },
                error: function() {
                    showAdminMessage('error', 'Upload failed. Please try again.');
                },
                complete: function() {
                    $progress.hide();
                    $submitBtn.prop('disabled', false).text('Upload CSV');
                }
            });
        });
    }

    /**
     * Initialize bid action buttons
     */
    function initBidActions() {
        // Accept bid
        $(document).on('click', '.usf-accept-bid', function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var bidId = $btn.data('bid-id');
            
            showBidActionModal('accept', bidId, function(adminNotes) {
                performBidAction('accept', bidId, adminNotes, $btn);
            });
        });

        // Reject bid
        $(document).on('click', '.usf-reject-bid', function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var bidId = $btn.data('bid-id');
            
            showBidActionModal('reject', bidId, function(adminNotes) {
                performBidAction('reject', bidId, adminNotes, $btn);
            });
        });
    }

    /**
     * Show bid action modal
     */
    function showBidActionModal(action, bidId, callback) {
        var title = action === 'accept' ? 'Accept Bid' : 'Reject Bid';
        var message = action === 'accept' ? 
            'Are you sure you want to accept this bid? This will create a WooCommerce order.' :
            'Are you sure you want to reject this bid?';

        var modalHtml = '<div class="usf-modal" id="usf-bid-action-modal">' +
            '<div class="usf-modal-content">' +
            '<div class="usf-modal-header">' +
            '<h3 class="usf-modal-title">' + title + '</h3>' +
            '<button class="usf-modal-close">&times;</button>' +
            '</div>' +
            '<p>' + message + '</p>' +
            '<div class="usf-form-group">' +
            '<label for="admin_notes">Admin Notes (optional):</label>' +
            '<textarea id="admin_notes" rows="3" placeholder="Add any notes about this decision..."></textarea>' +
            '</div>' +
            '<div class="usf-modal-actions">' +
            '<button class="button button-primary usf-confirm-action">' + title + '</button>' +
            '<button class="button usf-cancel-action">Cancel</button>' +
            '</div>' +
            '</div>' +
            '</div>';

        $('body').append(modalHtml);
        $('#usf-bid-action-modal').show();

        // Handle confirm
        $('.usf-confirm-action').on('click', function() {
            var adminNotes = $('#admin_notes').val();
            callback(adminNotes);
            $('#usf-bid-action-modal').remove();
        });

        // Handle cancel
        $('.usf-cancel-action, .usf-modal-close').on('click', function() {
            $('#usf-bid-action-modal').remove();
        });
    }

    /**
     * Perform bid action
     */
    function performBidAction(action, bidId, adminNotes, $btn) {
        var actionName = action === 'accept' ? 'usf_accept_bid' : 'usf_reject_bid';
        
        $btn.prop('disabled', true).text('Processing...');

        $.post(usf_admin_ajax.ajax_url, {
            action: actionName,
            bid_id: bidId,
            admin_notes: adminNotes,
            nonce: usf_admin_ajax.nonce
        })
        .done(function(response) {
            if (response.success) {
                showAdminMessage('success', response.data.message);
                
                // Update row status
                var $row = $btn.closest('tr');
                var statusClass = action === 'accept' ? 'usf-status-accepted' : 'usf-status-rejected';
                var statusText = action === 'accept' ? 'Accepted' : 'Rejected';
                
                $row.find('.usf-status-badge').removeClass().addClass('usf-status-badge ' + statusClass).text(statusText);
                $row.find('.usf-actions').html('<span class="description">Action completed</span>');
                
                // Refresh page after 2 seconds
                setTimeout(function() {
                    location.reload();
                }, 2000);
                
            } else {
                showAdminMessage('error', response.data.message);
                $btn.prop('disabled', false).text(action === 'accept' ? 'Accept' : 'Reject');
            }
        })
        .fail(function() {
            showAdminMessage('error', 'Action failed. Please try again.');
            $btn.prop('disabled', false).text(action === 'accept' ? 'Accept' : 'Reject');
        });
    }

    /**
     * Initialize bulk actions
     */
    function initBulkActions() {
        $('#usf-bulk-action-form').on('submit', function(e) {
            e.preventDefault();
            
            var action = $('#bulk-action-selector').val();
            var selectedBids = [];
            
            $('.usf-bid-checkbox:checked').each(function() {
                selectedBids.push($(this).val());
            });
            
            if (!action) {
                showAdminMessage('error', 'Please select an action.');
                return;
            }
            
            if (selectedBids.length === 0) {
                showAdminMessage('error', 'Please select at least one bid.');
                return;
            }
            
            var actionText = action === 'accept' ? 'accept' : 'reject';
            var confirmMessage = 'Are you sure you want to ' + actionText + ' ' + selectedBids.length + ' bid(s)?';
            
            if (confirm(confirmMessage)) {
                performBulkAction(action, selectedBids);
            }
        });
        
        // Select all checkbox
        $('#select-all-bids').on('change', function() {
            $('.usf-bid-checkbox').prop('checked', $(this).is(':checked'));
        });
    }

    /**
     * Perform bulk action
     */
    function performBulkAction(action, bidIds) {
        var $submitBtn = $('#usf-bulk-action-form button[type="submit"]');
        
        $submitBtn.prop('disabled', true).text('Processing...');
        
        $.post(usf_admin_ajax.ajax_url, {
            action: 'usf_bulk_bid_action',
            action_type: action,
            bid_ids: bidIds,
            admin_notes: '',
            nonce: usf_admin_ajax.nonce
        })
        .done(function(response) {
            if (response.success) {
                showAdminMessage('success', response.data.message);
                location.reload();
            } else {
                showAdminMessage('error', response.data.message);
            }
        })
        .fail(function() {
            showAdminMessage('error', 'Bulk action failed. Please try again.');
        })
        .always(function() {
            $submitBtn.prop('disabled', false).text('Apply');
        });
    }

    /**
     * Initialize modals
     */
    function initModals() {
        // Close modal when clicking outside
        $(document).on('click', '.usf-modal', function(e) {
            if (e.target === this) {
                $(this).remove();
            }
        });
        
        // Close modal with close button
        $(document).on('click', '.usf-modal-close', function() {
            $(this).closest('.usf-modal').remove();
        });
    }

    /**
     * Initialize filters
     */
    function initFilters() {
        // Auto-submit filter forms
        $('.usf-admin-filters select, .usf-admin-filters input').on('change', function() {
            $(this).closest('form').submit();
        });
        
        // Clear filters
        $('.usf-clear-filters').on('click', function(e) {
            e.preventDefault();
            var $form = $(this).closest('form');
            $form.find('select, input').val('');
            $form.submit();
        });
    }

    /**
     * Initialize email templates
     */
    function initEmailTemplates() {
        // Insert variable into template
        $(document).on('click', '.usf-variable-item', function() {
            var variable = $(this).text();
            var $textarea = $(this).closest('.usf-email-template').find('textarea');
            
            // Insert at cursor position
            var cursorPos = $textarea[0].selectionStart;
            var textBefore = $textarea.val().substring(0, cursorPos);
            var textAfter = $textarea.val().substring(cursorPos);
            
            $textarea.val(textBefore + variable + textAfter);
            
            // Move cursor after inserted variable
            $textarea[0].selectionStart = $textarea[0].selectionEnd = cursorPos + variable.length;
            $textarea.focus();
        });
        
        // Send test email
        $('.usf-send-test-email').on('click', function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var emailType = $btn.data('email-type');
            var recipientEmail = prompt('Enter email address to send test email to:');
            
            if (!recipientEmail) {
                return;
            }
            
            if (!isValidEmail(recipientEmail)) {
                showAdminMessage('error', 'Please enter a valid email address.');
                return;
            }
            
            $btn.prop('disabled', true).text('Sending...');
            
            $.post(usf_admin_ajax.ajax_url, {
                action: 'usf_send_test_email',
                email_type: emailType,
                recipient_email: recipientEmail,
                nonce: usf_admin_ajax.nonce
            })
            .done(function(response) {
                if (response.success) {
                    showAdminMessage('success', response.data.message);
                } else {
                    showAdminMessage('error', response.data.message);
                }
            })
            .fail(function() {
                showAdminMessage('error', 'Failed to send test email.');
            })
            .always(function() {
                $btn.prop('disabled', false).text('Send Test Email');
            });
        });
    }

    /**
     * Show admin message
     */
    function showAdminMessage(type, message) {
        var $message = $('<div class="usf-admin-message ' + type + '">' + message + '</div>');
        
        // Remove existing messages
        $('.usf-admin-message').remove();
        
        // Add new message at top of content
        $('.usf-admin-container, .wrap').first().prepend($message);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $message.fadeOut();
        }, 5000);
        
        // Scroll to top
        $('html, body').animate({ scrollTop: 0 }, 300);
    }

    /**
     * Validate email format
     */
    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Format currency
     */
    function formatCurrency(amount) {
        return '$' + parseFloat(amount).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
    }


    /**
     * Update lot status
     */
    $(document).on('change', '.usf-lot-status-select', function() {
        var $select = $(this);
        var lotId = $select.data('lot-id');
        var newStatus = $select.val();
        var originalStatus = $select.data('original-status');
        
        if (newStatus === originalStatus) {
            return;
        }
        
        var ajaxUrl = window.usfLotsData ? window.usfLotsData.ajaxUrl : usf_admin_ajax.ajax_url;
        var nonce = window.usfLotsData ? window.usfLotsData.nonce : usf_admin_ajax.nonce;
        
        $.post(ajaxUrl, {
            action: 'usf_update_lot_status',
            lot_id: lotId,
            status: newStatus,
            nonce: nonce
        })
        .done(function(response) {
            if (response.success) {
                showAdminMessage('success', response.data.message);
                $select.data('original-status', newStatus);
            } else {
                showAdminMessage('error', response.data.message);
                $select.val(originalStatus);
            }
        })
        .fail(function() {
            showAdminMessage('error', 'Status update failed. Please try again.');
            $select.val(originalStatus);
        });
    });

    /**
     * Initialize lots management functionality
     */
    function initLotsManagement() {
        // View lot functionality
        $(document).on('click', '.usf-view-lot', function(e) {
            e.preventDefault();
            
            var lotId = $(this).data('lot-id');
            if (!lotId) {
                showAdminMessage('error', 'Lot ID not found');
                return;
            }

            // Get the single auction URL from the global data
            var baseUrl = window.usfLotsData ? window.usfLotsData.singleAuctionUrl : '';
            if (!baseUrl) {
                showAdminMessage('error', 'Single auction page not configured');
                return;
            }

            // Build the URL with lot_id parameter
            var url = baseUrl + (baseUrl.indexOf('?') > -1 ? '&' : '?') + 'lot_id=' + encodeURIComponent(lotId);
            
            // Open in new tab
            window.open(url, '_blank');
        });

        // Delete lot functionality
        $(document).on('click', '.usf-delete-lot', function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var lotId = $btn.data('lot-id');
            var $row = $btn.closest('tr');
            
            if (!confirm('Are you sure you want to delete this auction lot? This action cannot be undone and will also delete all associated bids.')) {
                return;
            }
            
            $btn.prop('disabled', true).html('<span class="spinner"></span> Deleting...');
            
            var ajaxUrl = window.usfLotsData ? window.usfLotsData.ajaxUrl : usf_admin_ajax.ajax_url;
            var nonce = window.usfLotsData ? window.usfLotsData.nonce : usf_admin_ajax.nonce;
            
            $.post(ajaxUrl, {
                action: 'usf_delete_lot',
                lot_id: lotId,
                nonce: nonce
            })
            .done(function(response) {
                if (response.success) {
                    showAdminMessage('success', response.data.message);
                    $row.fadeOut(300, function() {
                        $(this).remove();
                    });
                } else {
                    showAdminMessage('error', response.data.message);
                    $btn.prop('disabled', false).html('<span class="dashicons dashicons-trash"></span> Delete');
                }
            })
            .fail(function() {
                showAdminMessage('error', 'Delete failed. Please try again.');
                $btn.prop('disabled', false).html('<span class="dashicons dashicons-trash"></span> Delete');
            });
        });

        // Inline editing functionality
        $(document).on('click', '.editable-field', function(e) {
            e.preventDefault();
            
            var $field = $(this);
            
            // Don't allow editing if already editing
            if ($field.hasClass('editing')) {
                return;
            }
            
            var field = $field.data('field');
            var lotId = $field.closest('tr').data('lot-id');
            var currentValue = $field.text().trim();
            
            // Remove formatting for editing
            var editValue = currentValue;
            if (field === 'min_offer') {
                editValue = currentValue.replace(/[$,]/g, '');
            } else if (field === 'closing_time') {
                // Convert display format back to input format
                var date = new Date(currentValue);
                if (!isNaN(date.getTime())) {
                    editValue = date.getFullYear() + '-' + 
                               String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                               String(date.getDate()).padStart(2, '0') + ' ' +
                               String(date.getHours()).padStart(2, '0') + ':' + 
                               String(date.getMinutes()).padStart(2, '0');
                }
            }
            
            $field.addClass('editing');
            
            var inputType = 'text';
            var inputAttrs = '';
            
            if (field === 'total_units') {
                inputType = 'number';
                inputAttrs = 'min="1" step="1"';
            } else if (field === 'min_offer') {
                inputType = 'number';
                inputAttrs = 'min="0" step="0.01"';
            } else if (field === 'closing_time') {
                inputType = 'datetime-local';
            }
            
            var $input = $('<input type="' + inputType + '" ' + inputAttrs + ' value="' + escapeHtml(editValue) + '">');
            var $controls = $('<div class="edit-controls">' +
                '<button type="button" class="button button-small button-primary save-edit">Save</button>' +
                '<button type="button" class="button button-small cancel-edit">Cancel</button>' +
                '</div>');
            
            $field.html($input).append($controls);
            $input.focus().select();
            
            // Handle save
            $controls.find('.save-edit').on('click', function() {
                saveFieldEdit($field, lotId, field, $input.val(), currentValue);
            });
            
            // Handle cancel
            $controls.find('.cancel-edit').on('click', function() {
                cancelFieldEdit($field, currentValue);
            });
            
            // Handle Enter key
            $input.on('keypress', function(e) {
                if (e.which === 13) {
                    saveFieldEdit($field, lotId, field, $input.val(), currentValue);
                }
            });
            
            // Handle Escape key
            $input.on('keyup', function(e) {
                if (e.which === 27) {
                    cancelFieldEdit($field, currentValue);
                }
            });
        });
    }

    /**
     * Save field edit
     */
    function saveFieldEdit($field, lotId, field, newValue, originalValue) {
        if (!newValue.trim()) {
            showAdminMessage('error', 'Value cannot be empty');
            return;
        }
        
        $field.addClass('loading');
        
        var ajaxUrl = window.usfLotsData ? window.usfLotsData.ajaxUrl : usf_admin_ajax.ajax_url;
        var nonce = window.usfLotsData ? window.usfLotsData.nonce : usf_admin_ajax.nonce;
        
        $.post(ajaxUrl, {
            action: 'usf_update_lot_field',
            lot_id: lotId,
            field: field,
            value: newValue,
            nonce: nonce
        })
        .done(function(response) {
            if (response.success) {
                showAdminMessage('success', response.data.message);
                $field.removeClass('editing loading').html(escapeHtml(response.data.display_value));
            } else {
                showAdminMessage('error', response.data.message);
                cancelFieldEdit($field, originalValue);
            }
        })
        .fail(function() {
            showAdminMessage('error', 'Update failed. Please try again.');
            cancelFieldEdit($field, originalValue);
        });
    }

    /**
     * Cancel field edit
     */
    function cancelFieldEdit($field, originalValue) {
        $field.removeClass('editing loading').html(escapeHtml(originalValue));
    }

    /**
     * Escape HTML
     */
    function escapeHtml(text) {
        var map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    /**
     * Enhanced admin message function for lots page
     */
    function showAdminMessage(type, message) {
        var $container = $('.usf-admin-message-container');
        if ($container.length === 0) {
            $container = $('.wrap').first();
        }
        
        var $message = $('<div class="usf-admin-message ' + type + '">' + message + '</div>');
        
        // Remove existing messages
        $('.usf-admin-message').remove();
        
        // Add new message
        $container.prepend($message);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $message.fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);
        
        // Scroll to top
        $('html, body').animate({ scrollTop: 0 }, 300);
    }

    // Initialize lots management when document is ready
    $(document).ready(function() {
        if ($('#usf-lots-table').length > 0) {
            initLotsManagement();
        }
    });

})(jQuery);
