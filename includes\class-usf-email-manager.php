<?php

/**
 * Email management functionality
 *
 * Handles all email notifications for the auction system
 */

class USF_Email_Manager {

    /**
     * Send bid confirmation email to bidder
     */
    public static function send_bid_confirmation($bid_id) {
        $bid = USF_Bid_Manager::get_bid_details($bid_id);
        if (!$bid) {
            return false;
        }

        $subject = USF_Database::get_setting('email_bid_confirmation_subject');
        $body = USF_Database::get_setting('email_bid_confirmation_body');

        $subject = self::replace_variables($subject, $bid);
        $body = self::replace_variables($body, $bid);

        $headers = array('Content-Type: text/html; charset=UTF-8');

        return wp_mail($bid->user_email, $subject, nl2br($body), $headers);
    }

    /**
     * Send admin notification email for new bid
     */
    public static function send_admin_notification($bid_id) {
        $bid = USF_Bid_Manager::get_bid_details($bid_id);
        if (!$bid) {
            return false;
        }

        $admin_email = USF_Database::get_setting('admin_email', get_option('admin_email'));
        $subject = USF_Database::get_setting('email_admin_notification_subject');
        $body = USF_Database::get_setting('email_admin_notification_body');

        $subject = self::replace_variables($subject, $bid);
        $body = self::replace_variables($body, $bid);

        // Add admin panel link
        $admin_url = admin_url('admin.php?page=usf-auction-bids');
        $body .= "\n\nView in admin panel: " . $admin_url;

        $headers = array('Content-Type: text/html; charset=UTF-8');

        return wp_mail($admin_email, $subject, nl2br($body), $headers);
    }

    /**
     * Send bid acceptance email to bidder
     */
    public static function send_bid_acceptance($bid_id) {
        $bid = USF_Bid_Manager::get_bid_details($bid_id);
        if (!$bid) {
            return false;
        }

        $subject = USF_Database::get_setting('email_bid_acceptance_subject');
        $body = USF_Database::get_setting('email_bid_acceptance_body');

        $subject = self::replace_variables($subject, $bid);
        $body = self::replace_variables($body, $bid);

        // Add order link if WooCommerce order exists
        if (!empty($bid->woocommerce_order_id)) {
            $order = wc_get_order($bid->woocommerce_order_id);
            if ($order) {
                $order_url = $order->get_checkout_payment_url();
                $body = str_replace('{order_link}', $order_url, $body);
            }
        } else {
            $body = str_replace('{order_link}', 'Payment link will be provided separately.', $body);
        }

        $headers = array('Content-Type: text/html; charset=UTF-8');

        return wp_mail($bid->user_email, $subject, nl2br($body), $headers);
    }

    /**
     * Send bid rejection email to bidder
     */
    public static function send_bid_rejection($bid_id) {
        $bid = USF_Bid_Manager::get_bid_details($bid_id);
        if (!$bid) {
            return false;
        }

        $subject = USF_Database::get_setting('email_bid_rejection_subject');
        $body = USF_Database::get_setting('email_bid_rejection_body');

        $subject = self::replace_variables($subject, $bid);
        $body = self::replace_variables($body, $bid);

        $headers = array('Content-Type: text/html; charset=UTF-8');

        return wp_mail($bid->user_email, $subject, nl2br($body), $headers);
    }

    /**
     * Replace email template variables
     */
    private static function replace_variables($content, $bid) {
        $variables = array(
            '{lot_id}' => $bid->lot_id,
            '{user_name}' => $bid->user_name,
            '{user_email}' => $bid->user_email,
            '{user_phone}' => $bid->user_phone,
            '{bid_amount}' => number_format($bid->bid_amount, 2),
            '{min_offer}' => number_format($bid->min_offer, 2),
            '{model}' => $bid->model,
            '{grade}' => $bid->grade,
            '{auction_house}' => $bid->auction_house,
            '{admin_notes}' => $bid->admin_notes,
            '{bid_time}' => date('F j, Y g:i A', strtotime($bid->bid_time)),
            '{closing_time}' => date('F j, Y g:i A', strtotime($bid->closing_time)),
            '{company_name}' => USF_Database::get_setting('company_name', get_bloginfo('name')),
            '{contact_phone}' => USF_Database::get_setting('contact_phone'),
            '{contact_address}' => USF_Database::get_setting('contact_address')
        );

        foreach ($variables as $variable => $value) {
            $content = str_replace($variable, $value, $content);
        }

        return $content;
    }

    /**
     * Send test email
     */
    public static function send_test_email($email_type, $recipient_email) {
        // Create a sample bid for testing
        $sample_bid = (object) array(
            'id' => 999,
            'lot_id' => 'TEST123',
            'user_name' => 'Test User',
            'user_email' => $recipient_email,
            'user_phone' => '(*************',
            'bid_amount' => 1500.00,
            'min_offer' => 1000.00,
            'model' => 'iPhone 13 Pro Max',
            'grade' => 'Grade A',
            'auction_house' => 'AT&T',
            'admin_notes' => 'This is a test email.',
            'bid_time' => current_time('mysql'),
            'closing_time' => date('Y-m-d H:i:s', strtotime('+2 days')),
            'woocommerce_order_id' => null
        );

        switch ($email_type) {
            case 'bid_confirmation':
                $subject = USF_Database::get_setting('email_bid_confirmation_subject');
                $body = USF_Database::get_setting('email_bid_confirmation_body');
                break;
            case 'admin_notification':
                $subject = USF_Database::get_setting('email_admin_notification_subject');
                $body = USF_Database::get_setting('email_admin_notification_body');
                break;
            case 'bid_acceptance':
                $subject = USF_Database::get_setting('email_bid_acceptance_subject');
                $body = USF_Database::get_setting('email_bid_acceptance_body');
                $body = str_replace('{order_link}', 'http://example.com/checkout', $body);
                break;
            case 'bid_rejection':
                $subject = USF_Database::get_setting('email_bid_rejection_subject');
                $body = USF_Database::get_setting('email_bid_rejection_body');
                break;
            default:
                return false;
        }

        $subject = '[TEST] ' . self::replace_variables($subject, $sample_bid);
        $body = "This is a test email.\n\n" . self::replace_variables($body, $sample_bid);

        $headers = array('Content-Type: text/html; charset=UTF-8');

        return wp_mail($recipient_email, $subject, nl2br($body), $headers);
    }

    /**
     * Get available email variables
     */
    public static function get_email_variables() {
        return array(
            '{lot_id}' => 'Auction lot ID',
            '{user_name}' => 'Bidder name',
            '{user_email}' => 'Bidder email',
            '{user_phone}' => 'Bidder phone',
            '{bid_amount}' => 'Bid amount (formatted)',
            '{min_offer}' => 'Minimum offer (formatted)',
            '{model}' => 'Device model',
            '{grade}' => 'Device grade',
            '{auction_house}' => 'Auction house name',
            '{admin_notes}' => 'Admin notes',
            '{bid_time}' => 'Bid submission time',
            '{closing_time}' => 'Auction closing time',
            '{order_link}' => 'Payment link (acceptance emails only)',
            '{company_name}' => 'Company name',
            '{contact_phone}' => 'Contact phone number',
            '{contact_address}' => 'Contact address'
        );
    }

    /**
     * Validate email template
     */
    public static function validate_email_template($template) {
        $errors = array();

        // Check for required variables in different template types
        if (strpos($template, '{lot_id}') === false) {
            $errors[] = 'Template should include {lot_id} variable';
        }

        if (strpos($template, '{user_name}') === false) {
            $errors[] = 'Template should include {user_name} variable';
        }

        // Check for potentially harmful content
        $dangerous_patterns = array(
            '/<script/i',
            '/javascript:/i',
            '/onclick=/i',
            '/onload=/i'
        );

        foreach ($dangerous_patterns as $pattern) {
            if (preg_match($pattern, $template)) {
                $errors[] = 'Template contains potentially harmful content';
                break;
            }
        }

        return array(
            'valid' => empty($errors),
            'errors' => $errors
        );
    }

    /**
     * Send bulk email to bidders
     */
    public static function send_bulk_email($subject, $message, $recipient_type = 'all', $filters = array()) {
        global $wpdb;

        $table_bids = $wpdb->prefix . 'auction_bids';
        
        // Build query based on recipient type
        $where_clauses = array();
        $where_values = array();

        switch ($recipient_type) {
            case 'pending':
                $where_clauses[] = "status = 'pending'";
                break;
            case 'accepted':
                $where_clauses[] = "status = 'accepted'";
                break;
            case 'rejected':
                $where_clauses[] = "status = 'rejected'";
                break;
            case 'all':
            default:
                // No additional filter
                break;
        }

        // Add date filter if specified
        if (!empty($filters['date_from'])) {
            $where_clauses[] = "bid_time >= %s";
            $where_values[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $where_clauses[] = "bid_time <= %s";
            $where_values[] = $filters['date_to'];
        }

        $where_sql = '';
        if (!empty($where_clauses)) {
            $where_sql = 'WHERE ' . implode(' AND ', $where_clauses);
        }

        $sql = "SELECT DISTINCT user_email, user_name FROM $table_bids $where_sql";

        if (!empty($where_values)) {
            $sql = $wpdb->prepare($sql, $where_values);
        }

        $recipients = $wpdb->get_results($sql);

        if (empty($recipients)) {
            return array(
                'success' => false,
                'message' => 'No recipients found'
            );
        }

        $sent_count = 0;
        $failed_count = 0;
        $headers = array('Content-Type: text/html; charset=UTF-8');

        foreach ($recipients as $recipient) {
            // Replace basic variables
            $personalized_subject = str_replace('{user_name}', $recipient->user_name, $subject);
            $personalized_message = str_replace('{user_name}', $recipient->user_name, $message);
            
            $result = wp_mail($recipient->user_email, $personalized_subject, nl2br($personalized_message), $headers);
            
            if ($result) {
                $sent_count++;
            } else {
                $failed_count++;
            }
        }

        return array(
            'success' => true,
            'sent' => $sent_count,
            'failed' => $failed_count,
            'total_recipients' => count($recipients)
        );
    }

    /**
     * Log email activity
     */
    public static function log_email($type, $recipient, $subject, $status) {
        // Simple logging - could be expanded to use a dedicated table
        error_log(sprintf(
            'USF Auction Email: %s | To: %s | Subject: %s | Status: %s',
            $type,
            $recipient,
            $subject,
            $status ? 'Sent' : 'Failed'
        ));
    }

    /**
     * Get email statistics
     */
    public static function get_email_stats() {
        // This would require a dedicated email log table for full statistics
        // For now, return basic information
        return array(
            'templates_configured' => 4,
            'last_test_email' => get_option('usf_auction_last_test_email', 'Never'),
            'admin_email' => USF_Database::get_setting('admin_email', get_option('admin_email'))
        );
    }
}
