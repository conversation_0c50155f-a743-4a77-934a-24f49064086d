<?php
/**
 * Admin Auction Houses Management View
 *
 * @package USF_Auction
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle form submissions
$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
$house_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$message = '';
$message_type = '';

// Handle add/edit form submission
if ($_POST && isset($_POST['auction_house_nonce']) && wp_verify_nonce($_POST['auction_house_nonce'], 'save_auction_house')) {
    $house_data = array(
        'name' => sanitize_text_field($_POST['name']),
        'slug' => sanitize_title($_POST['slug']),
        'description' => sanitize_textarea_field($_POST['description']),
        'assigned_page_id' => !empty($_POST['assigned_page_id']) ? intval($_POST['assigned_page_id']) : null,
        'status' => sanitize_text_field($_POST['status'])
    );

    // If editing, add the ID
    if ($house_id > 0) {
        $house_data['id'] = $house_id;
    }

    // Validate data
    $validation = USF_Auction_House_Helper::validate_auction_house_data($house_data);
    
    if ($validation['valid']) {
        $result = USF_Database::save_auction_house($house_data);
        
        if ($result) {
            $message = $house_id > 0 ? 'Auction house updated successfully!' : 'Auction house created successfully!';
            $message_type = 'success';
            
            // Reset form if adding new
            if ($house_id == 0) {
                $action = '';
                $house_id = 0;
            }
        } else {
            $message = 'Error saving auction house. Please try again.';
            $message_type = 'error';
        }
    } else {
        $message = 'Please fix the following errors: ' . implode(', ', $validation['errors']);
        $message_type = 'error';
    }
}

// Handle delete action
if ($action === 'delete' && $house_id > 0 && isset($_GET['_wpnonce']) && wp_verify_nonce($_GET['_wpnonce'], 'delete_auction_house_' . $house_id)) {
    $result = USF_Database::delete_auction_house($house_id);
    
    if ($result) {
        $message = 'Auction house deleted successfully!';
        $message_type = 'success';
    } else {
        $message = 'Error deleting auction house. Please try again.';
        $message_type = 'error';
    }
    
    $action = '';
    $house_id = 0;
}

// Get auction house data for editing
$house_data = null;
if ($action === 'edit' && $house_id > 0) {
    $house_data = USF_Database::get_auction_house($house_id);
    if (!$house_data) {
        $message = 'Auction house not found.';
        $message_type = 'error';
        $action = '';
        $house_id = 0;
    }
}

// Get all auction houses for listing
$auction_houses = USF_Database::get_auction_houses(array('status' => ''));

// Get all pages for dropdown
$pages = get_pages(array(
    'sort_column' => 'post_title',
    'sort_order' => 'ASC',
    'post_status' => 'publish'
));
?>

<div class="wrap">
    <h1>
        Auction Houses Management
        <?php if ($action !== 'add' && $action !== 'edit'): ?>
            <a href="<?php echo admin_url('admin.php?page=usf-auctions-houses&action=add'); ?>" class="page-title-action">Add New</a>
        <?php endif; ?>
    </h1>
    
    <?php if ($message): ?>
        <div class="notice notice-<?php echo esc_attr($message_type); ?> is-dismissible">
            <p><?php echo esc_html($message); ?></p>
        </div>
    <?php endif; ?>
    
    <?php if ($action === 'add' || $action === 'edit'): ?>
        <!-- Add/Edit Form -->
        <div class="usf-auction-house-form">
            <h2><?php echo $action === 'edit' ? 'Edit Auction House' : 'Add New Auction House'; ?></h2>
            
            <form method="post" action="">
                <?php wp_nonce_field('save_auction_house', 'auction_house_nonce'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="name">Name <span class="description">(required)</span></label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   value="<?php echo $house_data ? esc_attr($house_data->name) : ''; ?>" 
                                   class="regular-text" 
                                   required>
                            <p class="description">The display name of the auction house.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="slug">Slug <span class="description">(required)</span></label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="slug" 
                                   name="slug" 
                                   value="<?php echo $house_data ? esc_attr($house_data->slug) : ''; ?>" 
                                   class="regular-text" 
                                   required>
                            <p class="description">URL-friendly version of the name. Only lowercase letters, numbers, and hyphens allowed.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="description">Description</label>
                        </th>
                        <td>
                            <textarea id="description" 
                                      name="description" 
                                      rows="4" 
                                      cols="50" 
                                      class="large-text"><?php echo $house_data ? esc_textarea($house_data->description) : ''; ?></textarea>
                            <p class="description">Brief description of the auction house.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="assigned_page_id">Information Page</label>
                        </th>
                        <td>
                            <select name="assigned_page_id" id="assigned_page_id" class="regular-text">
                                <option value="">Select a page...</option>
                                <?php foreach ($pages as $page): ?>
                                    <option value="<?php echo esc_attr($page->ID); ?>" 
                                            <?php selected($house_data ? $house_data->assigned_page_id : '', $page->ID); ?>>
                                        <?php echo esc_html($page->post_title); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description">Select the page that contains information about this auction house. Auction house names will link to this page.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="status">Status</label>
                        </th>
                        <td>
                            <select name="status" id="status">
                                <option value="active" <?php selected($house_data ? $house_data->status : 'active', 'active'); ?>>Active</option>
                                <option value="inactive" <?php selected($house_data ? $house_data->status : '', 'inactive'); ?>>Inactive</option>
                            </select>
                            <p class="description">Only active auction houses will appear in filters and dropdowns.</p>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php echo $action === 'edit' ? 'Update Auction House' : 'Add Auction House'; ?>">
                    <a href="<?php echo admin_url('admin.php?page=usf-auctions-houses'); ?>" class="button">Cancel</a>
                </p>
            </form>
        </div>
        
    <?php else: ?>
        <!-- Auction Houses List -->
        <div class="usf-auction-houses-list">
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Slug</th>
                        <th>Assigned Page</th>
                        <th>Status</th>
                        <th>Lots Count</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($auction_houses)): ?>
                        <?php foreach ($auction_houses as $house): ?>
                            <?php 
                            $stats = USF_Auction_House_Helper::get_auction_house_stats($house->name);
                            $assigned_page = $house->assigned_page_id ? get_post($house->assigned_page_id) : null;
                            ?>
                            <tr>
                                <td><strong><?php echo esc_html($house->name); ?></strong></td>
                                <td><code><?php echo esc_html($house->slug); ?></code></td>
                                <td>
                                    <?php if ($assigned_page): ?>
                                        <a href="<?php echo get_permalink($assigned_page->ID); ?>" target="_blank">
                                            <?php echo esc_html($assigned_page->post_title); ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="description">No page assigned</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="usf-status-badge usf-status-<?php echo esc_attr($house->status); ?>">
                                        <?php echo esc_html(ucfirst($house->status)); ?>
                                    </span>
                                </td>
                                <td><?php echo esc_html($stats['total_lots']); ?> (<?php echo esc_html($stats['active_lots']); ?> active)</td>
                                <td>
                                    <a href="<?php echo admin_url('admin.php?page=usf-auctions-houses&action=edit&id=' . $house->id); ?>" class="button button-small">Edit</a>
                                    <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=usf-auctions-houses&action=delete&id=' . $house->id), 'delete_auction_house_' . $house->id); ?>" 
                                       class="button button-small" 
                                       onclick="return confirm('Are you sure you want to delete this auction house? This action cannot be undone.');">Delete</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6">No auction houses found. <a href="<?php echo admin_url('admin.php?page=usf-auctions-houses&action=add'); ?>">Add your first auction house</a>.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<style>
.usf-status-badge {
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.usf-status-active {
    background-color: #d4edda;
    color: #155724;
}

.usf-status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.usf-auction-house-form {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Auto-generate slug from name
    $('#name').on('input', function() {
        var name = $(this).val();
        var slug = name.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single
            .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
        
        $('#slug').val(slug);
    });
});
</script>
