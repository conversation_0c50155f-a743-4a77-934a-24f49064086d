<?php

/**
 * The admin-specific functionality of the plugin.
 *
 * @since      1.0.0
 *
 * @package    USF_Auction
 * @subpackage USF_Auction/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the admin-specific stylesheet and JavaScript.
 *
 * @package    USF_Auction
 * @subpackage USF_Auction/admin
 */
class USF_Auction_Admin {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param      string    $plugin_name       The name of this plugin.
     * @param      string    $version    The version of this plugin.
     */
    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Register the stylesheets for the admin area.
     *
     * @since    1.0.0
     */
    public function enqueue_styles() {
        wp_enqueue_style($this->plugin_name, USF_AUCTION_PLUGIN_URL . 'assets/css/admin.css', array(), $this->version, 'all');
    }

    /**
     * Register the JavaScript for the admin area.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts() {
        wp_enqueue_script($this->plugin_name, USF_AUCTION_PLUGIN_URL . 'assets/js/admin.js', array('jquery'), $this->version, false);
        
        // Localize script for AJAX
        wp_localize_script($this->plugin_name, 'usf_admin_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('usf_admin_action'),
            'upload_nonce' => wp_create_nonce('usf_upload_csv')
        ));
    }

    /**
     * Add admin menu
     *
     * @since    1.0.0
     */
    public function add_admin_menu() {
        add_menu_page(
            'USF Auctions',
            'USF Auctions',
            'manage_options',
            'usf-auctions',
            array($this, 'display_dashboard'),
            'dashicons-hammer',
            30
        );

        add_submenu_page(
            'usf-auctions',
            'Dashboard',
            'Dashboard',
            'manage_options',
            'usf-auctions',
            array($this, 'display_dashboard')
        );

        add_submenu_page(
            'usf-auctions',
            'Upload CSV',
            'Upload CSV',
            'manage_options',
            'usf-auctions-upload',
            array($this, 'display_upload')
        );

        add_submenu_page(
            'usf-auctions',
            'Manage Lots',
            'Manage Lots',
            'manage_options',
            'usf-auctions-lots',
            array($this, 'display_lots')
        );

        add_submenu_page(
            'usf-auctions',
            'Manage Bids',
            'Manage Bids',
            'manage_options',
            'usf-auctions-bids',
            array($this, 'display_bids')
        );

        add_submenu_page(
            'usf-auctions',
            'Auction Houses',
            'Auction Houses',
            'manage_options',
            'usf-auctions-houses',
            array($this, 'display_auction_houses')
        );

        add_submenu_page(
            'usf-auctions',
            'Settings',
            'Settings',
            'manage_options',
            'usf-auctions-settings',
            array($this, 'display_settings')
        );
    }

    /**
     * Initialize admin settings
     *
     * @since    1.0.0
     */
    public function admin_init() {
        // Register settings
        register_setting('usf_auction_settings', 'usf_auction_email_templates');
        register_setting('usf_auction_settings', 'usf_auction_general_settings');
    }

    /**
     * Display dashboard page
     *
     * @since    1.0.0
     */
    public function display_dashboard() {
        include_once USF_AUCTION_PLUGIN_DIR . 'admin/views/dashboard.php';
    }

    /**
     * Display upload page
     *
     * @since    1.0.0
     */
    public function display_upload() {
        include_once USF_AUCTION_PLUGIN_DIR . 'admin/views/upload.php';
    }

    /**
     * Display lots management page
     *
     * @since    1.0.0
     */
    public function display_lots() {
        include_once USF_AUCTION_PLUGIN_DIR . 'admin/views/lots.php';
    }

    /**
     * Display bids management page
     *
     * @since    1.0.0
     */
    public function display_bids() {
        include_once USF_AUCTION_PLUGIN_DIR . 'admin/views/bids.php';
    }

    /**
     * Display auction houses management page
     *
     * @since    1.0.0
     */
    public function display_auction_houses() {
        include_once USF_AUCTION_PLUGIN_DIR . 'admin/views/auction-houses.php';
    }

    /**
     * Display settings page
     *
     * @since    1.0.0
     */
    public function display_settings() {
        include_once USF_AUCTION_PLUGIN_DIR . 'admin/views/settings.php';
    }
}
